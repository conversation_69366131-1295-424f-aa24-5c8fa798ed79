[{"D:\\DataGen\\authkey-chat-new\\src\\index.js": "1", "D:\\DataGen\\authkey-chat-new\\src\\App.js": "2", "D:\\DataGen\\authkey-chat-new\\src\\context\\ChatContext.js": "3", "D:\\DataGen\\authkey-chat-new\\src\\context\\AuthContext.js": "4", "D:\\DataGen\\authkey-chat-new\\src\\context\\AllProviders.js": "5", "D:\\DataGen\\authkey-chat-new\\src\\utils\\Utils.js": "6", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Login.jsx": "7", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentManagementPage.jsx": "8", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Home.jsx": "9", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentReportPage.jsx": "10", "D:\\DataGen\\authkey-chat-new\\src\\pages\\CreateAgentPage.jsx": "11", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Setting.jsx": "12", "D:\\DataGen\\authkey-chat-new\\src\\components\\VerifyAccount.jsx": "13", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\CustomerView.jsx": "14", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\Customers.jsx": "15", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Report\\Report.jsx": "16", "D:\\DataGen\\authkey-chat-new\\src\\components\\BroadcastReport\\BroadcastReport.jsx": "17", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentReport\\AgentReport.jsx": "18", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentSettings\\AgentSettings.jsx": "19", "D:\\DataGen\\authkey-chat-new\\src\\api\\api.js": "20", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\CreateNewPipeline.jsx": "21", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LeadsDashboard.jsx": "22", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentDetails\\AgentDetailPage.jsx": "23", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakStatusCard.jsx": "24", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\CustomerReport.jsx": "25", "D:\\DataGen\\authkey-chat-new\\src\\components\\LeftMenu.jsx": "26", "D:\\DataGen\\authkey-chat-new\\src\\components\\Navbar.jsx": "27", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentManagement.jsx": "28", "D:\\DataGen\\authkey-chat-new\\src\\components\\Offline.jsx": "29", "D:\\DataGen\\authkey-chat-new\\src\\components\\Chat.jsx": "30", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useDebounce.js": "31", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useMaskNo.js": "32", "D:\\DataGen\\authkey-chat-new\\src\\components\\Sidebar.jsx": "33", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\ActivityLog.jsx": "34", "D:\\DataGen\\authkey-chat-new\\src\\components\\ui-conponents\\NewChatCall.jsx": "35", "D:\\DataGen\\authkey-chat-new\\src\\components\\Contactdetailcard\\ContactDetailCard.jsx": "36", "D:\\DataGen\\authkey-chat-new\\src\\components\\QuickReply\\QuickReply.jsx": "37", "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\FaqPanel.jsx": "38", "D:\\DataGen\\authkey-chat-new\\src\\components\\Notescard\\NotesCard.jsx": "39", "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\Faqsettings.jsx": "40", "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\LabelCard.jsx": "41", "D:\\DataGen\\authkey-chat-new\\src\\components\\NewDashboard\\NewDashboard.jsx": "42", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelSelector.jsx": "43", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\StageCard.jsx": "44", "D:\\DataGen\\authkey-chat-new\\src\\components\\AutoReply\\AutoReplyRules.jsx": "45", "D:\\DataGen\\authkey-chat-new\\src\\components\\DeleteModal\\DeleteModal.jsx": "46", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelColumn.jsx": "47", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactCard.jsx": "48", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\FilterSortBar.jsx": "49", "D:\\DataGen\\authkey-chat-new\\src\\components\\Broadcast\\Broadcast.jsx": "50", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\AssignModal.jsx": "51", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useBlockUser.js": "52", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messages.jsx": "53", "D:\\DataGen\\authkey-chat-new\\src\\components\\Search.jsx": "54", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useReminders.js": "55", "D:\\DataGen\\authkey-chat-new\\src\\components\\Welcome.jsx": "56", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakModal.jsx": "57", "D:\\DataGen\\authkey-chat-new\\src\\components\\Chats.jsx": "58", "D:\\DataGen\\authkey-chat-new\\src\\components\\ChangePassword.jsx": "59", "D:\\DataGen\\authkey-chat-new\\src\\components\\bookmark\\Bookmark.js": "60", "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\Labels.jsx": "61", "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\Reminder.jsx": "62", "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\RemiderToast.jsx": "63", "D:\\DataGen\\authkey-chat-new\\src\\components\\profile\\UserProfile.jsx": "64", "D:\\DataGen\\authkey-chat-new\\src\\components\\InputBar\\Inputbar.jsx": "65", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactActivityPopup.jsx": "66", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChartSection.jsx": "67", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\Table.jsx": "68", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\TodayStats.jsx": "69", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OverviewCard.jsx": "70", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChatOverviewCard.jsx": "71", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerHistory\\CustomerHistoryModal.jsx": "72", "D:\\DataGen\\authkey-chat-new\\src\\components\\TemplatePrev.jsx": "73", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useSentences.js": "74", "D:\\DataGen\\authkey-chat-new\\src\\components\\Message.jsx": "75", "D:\\DataGen\\authkey-chat-new\\src\\components\\BlockCard.jsx": "76", "D:\\DataGen\\authkey-chat-new\\src\\components\\Input.jsx": "77", "D:\\DataGen\\authkey-chat-new\\src\\components\\WaitingCard\\WaitingCard.jsx": "78", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReplyPreview\\ReplyPreview.jsx": "79", "D:\\DataGen\\authkey-chat-new\\src\\components\\SendTemplate.jsx": "80", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\MessageCard.jsx": "81", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\ReplyDropdown.jsx": "82", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\OrderModal.jsx": "83", "D:\\DataGen\\authkey-chat-new\\src\\components\\MenuList\\MenuList.jsx": "84", "D:\\DataGen\\authkey-chat-new\\src\\config\\translation.js": "85", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OnlineUsersCount.jsx": "86", "D:\\DataGen\\authkey-chat-new\\src\\utils\\countryFlags.js": "87", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\PageAnalyticsDashboard.jsx": "88"}, {"size": 772, "mtime": 1753168233939, "results": "89", "hashOfConfig": "90"}, {"size": 8668, "mtime": 1753168233729, "results": "91", "hashOfConfig": "90"}, {"size": 807, "mtime": 1753168233936, "results": "92", "hashOfConfig": "90"}, {"size": 719, "mtime": 1753168233936, "results": "93", "hashOfConfig": "90"}, {"size": 30299, "mtime": 1753871088512, "results": "94", "hashOfConfig": "90"}, {"size": 4276, "mtime": 1753168233951, "results": "95", "hashOfConfig": "90"}, {"size": 7124, "mtime": 1753168233948, "results": "96", "hashOfConfig": "90"}, {"size": 496, "mtime": 1753168233940, "results": "97", "hashOfConfig": "90"}, {"size": 8589, "mtime": 1753168233944, "results": "98", "hashOfConfig": "90"}, {"size": 11705, "mtime": 1753168233941, "results": "99", "hashOfConfig": "90"}, {"size": 13433, "mtime": 1753168233942, "results": "100", "hashOfConfig": "90"}, {"size": 36897, "mtime": 1753168233950, "results": "101", "hashOfConfig": "90"}, {"size": 18151, "mtime": 1753168233929, "results": "102", "hashOfConfig": "90"}, {"size": 11949, "mtime": 1753168233943, "results": "103", "hashOfConfig": "90"}, {"size": 34651, "mtime": 1753168233943, "results": "104", "hashOfConfig": "90"}, {"size": 7225, "mtime": 1753168233949, "results": "105", "hashOfConfig": "90"}, {"size": 8527, "mtime": 1753168233911, "results": "106", "hashOfConfig": "90"}, {"size": 5737, "mtime": 1753168233908, "results": "107", "hashOfConfig": "90"}, {"size": 7865, "mtime": 1753168233941, "results": "108", "hashOfConfig": "90"}, {"size": 670, "mtime": 1753168233732, "results": "109", "hashOfConfig": "90"}, {"size": 11153, "mtime": 1753168233946, "results": "110", "hashOfConfig": "90"}, {"size": 21545, "mtime": 1753168233947, "results": "111", "hashOfConfig": "90"}, {"size": 29861, "mtime": 1753168233940, "results": "112", "hashOfConfig": "90"}, {"size": 3983, "mtime": 1753168233907, "results": "113", "hashOfConfig": "90"}, {"size": 35976, "mtime": 1753168233915, "results": "114", "hashOfConfig": "90"}, {"size": 9016, "mtime": 1753168233918, "results": "115", "hashOfConfig": "90"}, {"size": 2426, "mtime": 1753168233921, "results": "116", "hashOfConfig": "90"}, {"size": 51998, "mtime": 1753168233908, "results": "117", "hashOfConfig": "90"}, {"size": 720, "mtime": 1753168233922, "results": "118", "hashOfConfig": "90"}, {"size": 19809, "mtime": 1753168233912, "results": "119", "hashOfConfig": "90"}, {"size": 422, "mtime": 1753168233937, "results": "120", "hashOfConfig": "90"}, {"size": 415, "mtime": 1753168233937, "results": "121", "hashOfConfig": "90"}, {"size": 47488, "mtime": 1753168233928, "results": "122", "hashOfConfig": "90"}, {"size": 3273, "mtime": 1753168233943, "results": "123", "hashOfConfig": "90"}, {"size": 5900, "mtime": 1753168233935, "results": "124", "hashOfConfig": "90"}, {"size": 15841, "mtime": 1753168233914, "results": "125", "hashOfConfig": "90"}, {"size": 23234, "mtime": 1753168233923, "results": "126", "hashOfConfig": "90"}, {"size": 4387, "mtime": 1753168233932, "results": "127", "hashOfConfig": "90"}, {"size": 12941, "mtime": 1753168233922, "results": "128", "hashOfConfig": "90"}, {"size": 14032, "mtime": 1753168233932, "results": "129", "hashOfConfig": "90"}, {"size": 1807, "mtime": 1753168233917, "results": "130", "hashOfConfig": "90"}, {"size": 10973, "mtime": 1753790857256, "results": "131", "hashOfConfig": "90"}, {"size": 1911, "mtime": 1753168233947, "results": "132", "hashOfConfig": "90"}, {"size": 1641, "mtime": 1753168233948, "results": "133", "hashOfConfig": "90"}, {"size": 8940, "mtime": 1753168233909, "results": "134", "hashOfConfig": "90"}, {"size": 894, "mtime": 1753168233916, "results": "135", "hashOfConfig": "90"}, {"size": 1864, "mtime": 1753168233946, "results": "136", "hashOfConfig": "90"}, {"size": 4796, "mtime": 1753168233945, "results": "137", "hashOfConfig": "90"}, {"size": 5430, "mtime": 1753168233946, "results": "138", "hashOfConfig": "90"}, {"size": 34730, "mtime": 1753168233910, "results": "139", "hashOfConfig": "90"}, {"size": 1411, "mtime": 1753168233915, "results": "140", "hashOfConfig": "90"}, {"size": 1863, "mtime": 1753168233937, "results": "141", "hashOfConfig": "90"}, {"size": 3571, "mtime": 1753168233921, "results": "142", "hashOfConfig": "90"}, {"size": 3661, "mtime": 1753168233927, "results": "143", "hashOfConfig": "90"}, {"size": 3345, "mtime": 1753168233938, "results": "144", "hashOfConfig": "90"}, {"size": 1092, "mtime": 1753168233930, "results": "145", "hashOfConfig": "90"}, {"size": 6060, "mtime": 1753168233907, "results": "146", "hashOfConfig": "90"}, {"size": 65894, "mtime": 1753168233913, "results": "147", "hashOfConfig": "90"}, {"size": 5114, "mtime": 1753168233911, "results": "148", "hashOfConfig": "90"}, {"size": 6474, "mtime": 1753168233932, "results": "149", "hashOfConfig": "90"}, {"size": 2511, "mtime": 1753168233917, "results": "150", "hashOfConfig": "90"}, {"size": 23187, "mtime": 1753168233924, "results": "151", "hashOfConfig": "90"}, {"size": 1727, "mtime": 1753168233923, "results": "152", "hashOfConfig": "90"}, {"size": 6231, "mtime": 1753168233933, "results": "153", "hashOfConfig": "90"}, {"size": 28333, "mtime": 1753859576542, "results": "154", "hashOfConfig": "90"}, {"size": 3229, "mtime": 1753168233945, "results": "155", "hashOfConfig": "90"}, {"size": 1903, "mtime": 1753168233926, "results": "156", "hashOfConfig": "90"}, {"size": 7548, "mtime": 1753168233927, "results": "157", "hashOfConfig": "90"}, {"size": 2711, "mtime": 1753771150110, "results": "158", "hashOfConfig": "90"}, {"size": 1749, "mtime": 1753168233926, "results": "159", "hashOfConfig": "90"}, {"size": 6574, "mtime": 1753168233926, "results": "160", "hashOfConfig": "90"}, {"size": 4590, "mtime": 1753168233914, "results": "161", "hashOfConfig": "90"}, {"size": 5836, "mtime": 1753168233929, "results": "162", "hashOfConfig": "90"}, {"size": 3091, "mtime": 1753168233938, "results": "163", "hashOfConfig": "90"}, {"size": 23211, "mtime": 1753168233919, "results": "164", "hashOfConfig": "90"}, {"size": 886, "mtime": 1753168233910, "results": "165", "hashOfConfig": "90"}, {"size": 41532, "mtime": 1753181213943, "results": "166", "hashOfConfig": "90"}, {"size": 5348, "mtime": 1753168233930, "results": "167", "hashOfConfig": "90"}, {"size": 6572, "mtime": 1753168233925, "results": "168", "hashOfConfig": "90"}, {"size": 14476, "mtime": 1753168233928, "results": "169", "hashOfConfig": "90"}, {"size": 15277, "mtime": 1753168233920, "results": "170", "hashOfConfig": "90"}, {"size": 519, "mtime": 1753168233920, "results": "171", "hashOfConfig": "90"}, {"size": 1780, "mtime": 1753168233920, "results": "172", "hashOfConfig": "90"}, {"size": 6056, "mtime": 1753168233919, "results": "173", "hashOfConfig": "90"}, {"size": 528, "mtime": 1753175305151, "results": "174", "hashOfConfig": "90"}, {"size": 34087, "mtime": 1753965803900, "results": "175", "hashOfConfig": "90"}, {"size": 4217, "mtime": 1753782273610, "results": "176", "hashOfConfig": "90"}, {"size": 26415, "mtime": 1753965904616, "results": "177", "hashOfConfig": "90"}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18psi8c", {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\DataGen\\authkey-chat-new\\src\\index.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\App.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\ChatContext.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\AuthContext.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\AllProviders.js", ["442"], [], "D:\\DataGen\\authkey-chat-new\\src\\utils\\Utils.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Login.jsx", ["443"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentManagementPage.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Home.jsx", ["444", "445", "446"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentReportPage.jsx", ["447", "448", "449", "450", "451"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\CreateAgentPage.jsx", ["452", "453"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Setting.jsx", ["454", "455", "456", "457", "458", "459", "460", "461", "462", "463"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\VerifyAccount.jsx", ["464"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\CustomerView.jsx", ["465", "466", "467"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\Customers.jsx", ["468", "469", "470", "471", "472", "473", "474", "475"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Report\\Report.jsx", ["476", "477"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\BroadcastReport\\BroadcastReport.jsx", ["478", "479"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentReport\\AgentReport.jsx", ["480"], ["481"], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentSettings\\AgentSettings.jsx", ["482", "483"], [], "D:\\DataGen\\authkey-chat-new\\src\\api\\api.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\CreateNewPipeline.jsx", ["484"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LeadsDashboard.jsx", ["485", "486", "487", "488", "489"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentDetails\\AgentDetailPage.jsx", ["490", "491", "492", "493", "494", "495", "496", "497", "498"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakStatusCard.jsx", ["499"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\CustomerReport.jsx", ["500", "501", "502", "503", "504", "505", "506", "507", "508"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\LeftMenu.jsx", ["509"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Navbar.jsx", ["510"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentManagement.jsx", ["511", "512", "513", "514", "515", "516"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Offline.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Chat.jsx", ["517", "518", "519", "520", "521"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useDebounce.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useMaskNo.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Sidebar.jsx", ["522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\ActivityLog.jsx", ["537"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ui-conponents\\NewChatCall.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Contactdetailcard\\ContactDetailCard.jsx", ["538"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\QuickReply\\QuickReply.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\FaqPanel.jsx", ["539"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Notescard\\NotesCard.jsx", ["540"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\Faqsettings.jsx", ["541", "542"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\LabelCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\NewDashboard\\NewDashboard.jsx", ["543"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelSelector.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\StageCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AutoReply\\AutoReplyRules.jsx", ["544"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\DeleteModal\\DeleteModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelColumn.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactCard.jsx", ["545", "546", "547", "548"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\FilterSortBar.jsx", ["549", "550"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Broadcast\\Broadcast.jsx", ["551", "552", "553", "554", "555"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\AssignModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useBlockUser.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messages.jsx", ["556"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Search.jsx", ["557"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useReminders.js", ["558"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Welcome.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakModal.jsx", ["559"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Chats.jsx", ["560", "561", "562", "563", "564", "565", "566", "567", "568"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ChangePassword.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\bookmark\\Bookmark.js", ["569"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\Labels.jsx", ["570", "571", "572", "573", "574", "575"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\Reminder.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\RemiderToast.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\profile\\UserProfile.jsx", ["576", "577", "578", "579", "580"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\InputBar\\Inputbar.jsx", ["581", "582", "583", "584", "585", "586", "587", "588", "589", "590"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactActivityPopup.jsx", ["591"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChartSection.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\Table.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\TodayStats.jsx", ["592", "593", "594"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OverviewCard.jsx", ["595"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChatOverviewCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerHistory\\CustomerHistoryModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\TemplatePrev.jsx", ["596"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useSentences.js", ["597", "598", "599"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Message.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\BlockCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Input.jsx", ["600", "601", "602", "603", "604", "605", "606"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\WaitingCard\\WaitingCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReplyPreview\\ReplyPreview.jsx", ["607"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\SendTemplate.jsx", ["608"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\MessageCard.jsx", ["609", "610"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\ReplyDropdown.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\OrderModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\MenuList\\MenuList.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\config\\translation.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OnlineUsersCount.jsx", ["611"], [], "D:\\DataGen\\authkey-chat-new\\src\\utils\\countryFlags.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\PageAnalyticsDashboard.jsx", ["612", "613"], [], {"ruleId": "614", "severity": 1, "message": "615", "line": 386, "column": 6, "nodeType": "616", "endLine": 386, "endColumn": 19, "suggestions": "617"}, {"ruleId": "618", "severity": 1, "message": "619", "line": 1, "column": 17, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 26}, {"ruleId": "614", "severity": 1, "message": "622", "line": 60, "column": 6, "nodeType": "616", "endLine": 60, "endColumn": 19, "suggestions": "623"}, {"ruleId": "614", "severity": 1, "message": "624", "line": 101, "column": 6, "nodeType": "616", "endLine": 101, "endColumn": 27, "suggestions": "625"}, {"ruleId": "614", "severity": 1, "message": "626", "line": 166, "column": 5, "nodeType": "616", "endLine": 166, "endColumn": 73, "suggestions": "627"}, {"ruleId": "618", "severity": 1, "message": "628", "line": 9, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 9, "endColumn": 14}, {"ruleId": "618", "severity": 1, "message": "629", "line": 33, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 33, "endColumn": 22}, {"ruleId": "614", "severity": 1, "message": "630", "line": 187, "column": 6, "nodeType": "616", "endLine": 187, "endColumn": 19, "suggestions": "631"}, {"ruleId": "614", "severity": 1, "message": "632", "line": 191, "column": 6, "nodeType": "616", "endLine": 191, "endColumn": 19, "suggestions": "633"}, {"ruleId": "614", "severity": 1, "message": "634", "line": 238, "column": 6, "nodeType": "616", "endLine": 238, "endColumn": 8, "suggestions": "635"}, {"ruleId": "618", "severity": 1, "message": "636", "line": 30, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 30, "endColumn": 21}, {"ruleId": "614", "severity": 1, "message": "637", "line": 35, "column": 6, "nodeType": "616", "endLine": 35, "endColumn": 8, "suggestions": "638"}, {"ruleId": "618", "severity": 1, "message": "639", "line": 17, "column": 26, "nodeType": "620", "messageId": "621", "endLine": 17, "endColumn": 43}, {"ruleId": "618", "severity": 1, "message": "640", "line": 43, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 43, "endColumn": 22}, {"ruleId": "618", "severity": 1, "message": "641", "line": 43, "column": 24, "nodeType": "620", "messageId": "621", "endLine": 43, "endColumn": 39}, {"ruleId": "618", "severity": 1, "message": "642", "line": 107, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 107, "endColumn": 37}, {"ruleId": "618", "severity": 1, "message": "643", "line": 113, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 113, "endColumn": 28}, {"ruleId": "618", "severity": 1, "message": "644", "line": 117, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 117, "endColumn": 31}, {"ruleId": "614", "severity": 1, "message": "645", "line": 207, "column": 6, "nodeType": "616", "endLine": 207, "endColumn": 35, "suggestions": "646"}, {"ruleId": "614", "severity": 1, "message": "647", "line": 317, "column": 6, "nodeType": "616", "endLine": 317, "endColumn": 19, "suggestions": "648"}, {"ruleId": "618", "severity": 1, "message": "649", "line": 337, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 337, "endColumn": 15}, {"ruleId": "618", "severity": 1, "message": "650", "line": 354, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 354, "endColumn": 18}, {"ruleId": "614", "severity": 1, "message": "651", "line": 30, "column": 6, "nodeType": "616", "endLine": 30, "endColumn": 8, "suggestions": "652"}, {"ruleId": "618", "severity": 1, "message": "653", "line": 16, "column": 26, "nodeType": "620", "messageId": "621", "endLine": 16, "endColumn": 41}, {"ruleId": "618", "severity": 1, "message": "654", "line": 36, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 36, "endColumn": 19}, {"ruleId": "614", "severity": 1, "message": "655", "line": 44, "column": 8, "nodeType": "616", "endLine": 44, "endColumn": 29, "suggestions": "656"}, {"ruleId": "618", "severity": 1, "message": "657", "line": 2, "column": 51, "nodeType": "620", "messageId": "621", "endLine": 2, "endColumn": 62}, {"ruleId": "618", "severity": 1, "message": "658", "line": 32, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 32, "endColumn": 26}, {"ruleId": "614", "severity": 1, "message": "632", "line": 71, "column": 8, "nodeType": "616", "endLine": 71, "endColumn": 39, "suggestions": "659"}, {"ruleId": "614", "severity": 1, "message": "660", "line": 132, "column": 8, "nodeType": "616", "endLine": 132, "endColumn": 30, "suggestions": "661"}, {"ruleId": "618", "severity": 1, "message": "662", "line": 330, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 330, "endColumn": 26}, {"ruleId": "618", "severity": 1, "message": "663", "line": 348, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 348, "endColumn": 22}, {"ruleId": "618", "severity": 1, "message": "664", "line": 363, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 363, "endColumn": 25}, {"ruleId": "618", "severity": 1, "message": "665", "line": 434, "column": 19, "nodeType": "620", "messageId": "621", "endLine": 434, "endColumn": 27}, {"ruleId": "618", "severity": 1, "message": "666", "line": 5, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 5, "endColumn": 20}, {"ruleId": "618", "severity": 1, "message": "667", "line": 14, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 14, "endColumn": 22}, {"ruleId": "614", "severity": 1, "message": "668", "line": 29, "column": 8, "nodeType": "616", "endLine": 29, "endColumn": 21, "suggestions": "669"}, {"ruleId": "614", "severity": 1, "message": "670", "line": 32, "column": 8, "nodeType": "616", "endLine": 32, "endColumn": 42, "suggestions": "671"}, {"ruleId": "614", "severity": 1, "message": "672", "line": 28, "column": 6, "nodeType": "616", "endLine": 28, "endColumn": 19, "suggestions": "673"}, {"ruleId": "614", "severity": 1, "message": "674", "line": 33, "column": 6, "nodeType": "616", "endLine": 33, "endColumn": 40, "suggestions": "675", "suppressions": "676"}, {"ruleId": "618", "severity": 1, "message": "677", "line": 24, "column": 19, "nodeType": "620", "messageId": "621", "endLine": 24, "endColumn": 29}, {"ruleId": "614", "severity": 1, "message": "678", "line": 39, "column": 6, "nodeType": "616", "endLine": 39, "endColumn": 28, "suggestions": "679"}, {"ruleId": "618", "severity": 1, "message": "680", "line": 23, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 23, "endColumn": 26}, {"ruleId": "618", "severity": 1, "message": "681", "line": 14, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 14, "endColumn": 19}, {"ruleId": "682", "severity": 1, "message": "683", "line": 65, "column": 76, "nodeType": "684", "messageId": "685", "endLine": 65, "endColumn": 78}, {"ruleId": "686", "severity": 1, "message": "687", "line": 68, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 74, "endColumn": 10}, {"ruleId": "614", "severity": 1, "message": "690", "line": 205, "column": 8, "nodeType": "616", "endLine": 205, "endColumn": 39, "suggestions": "691"}, {"ruleId": "614", "severity": 1, "message": "692", "line": 210, "column": 8, "nodeType": "616", "endLine": 210, "endColumn": 21, "suggestions": "693"}, {"ruleId": "618", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 3, "endColumn": 14}, {"ruleId": "618", "severity": 1, "message": "695", "line": 5, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 5, "endColumn": 15}, {"ruleId": "618", "severity": 1, "message": "696", "line": 7, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 7, "endColumn": 12}, {"ruleId": "618", "severity": 1, "message": "697", "line": 22, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 22, "endColumn": 22}, {"ruleId": "618", "severity": 1, "message": "698", "line": 23, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 23, "endColumn": 14}, {"ruleId": "618", "severity": 1, "message": "680", "line": 24, "column": 18, "nodeType": "620", "messageId": "621", "endLine": 24, "endColumn": 34}, {"ruleId": "618", "severity": 1, "message": "699", "line": 53, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 53, "endColumn": 16}, {"ruleId": "614", "severity": 1, "message": "700", "line": 104, "column": 8, "nodeType": "616", "endLine": 104, "endColumn": 48, "suggestions": "701"}, {"ruleId": "618", "severity": 1, "message": "702", "line": 123, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 123, "endColumn": 21}, {"ruleId": "614", "severity": 1, "message": "703", "line": 50, "column": 6, "nodeType": "616", "endLine": 50, "endColumn": 17, "suggestions": "704"}, {"ruleId": "618", "severity": 1, "message": "705", "line": 3, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 3, "endColumn": 18}, {"ruleId": "618", "severity": 1, "message": "658", "line": 33, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 33, "endColumn": 26}, {"ruleId": "618", "severity": 1, "message": "706", "line": 33, "column": 28, "nodeType": "620", "messageId": "621", "endLine": 33, "endColumn": 45}, {"ruleId": "618", "severity": 1, "message": "707", "line": 34, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 34, "endColumn": 25}, {"ruleId": "618", "severity": 1, "message": "708", "line": 34, "column": 27, "nodeType": "620", "messageId": "621", "endLine": 34, "endColumn": 43}, {"ruleId": "618", "severity": 1, "message": "709", "line": 48, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 48, "endColumn": 31}, {"ruleId": "614", "severity": 1, "message": "710", "line": 81, "column": 8, "nodeType": "616", "endLine": 81, "endColumn": 21, "suggestions": "711"}, {"ruleId": "614", "severity": 1, "message": "712", "line": 87, "column": 8, "nodeType": "616", "endLine": 87, "endColumn": 39, "suggestions": "713"}, {"ruleId": "618", "severity": 1, "message": "665", "line": 414, "column": 19, "nodeType": "620", "messageId": "621", "endLine": 414, "endColumn": 27}, {"ruleId": "618", "severity": 1, "message": "714", "line": 8, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 8, "endColumn": 24}, {"ruleId": "618", "severity": 1, "message": "715", "line": 6, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 6, "endColumn": 20}, {"ruleId": "618", "severity": 1, "message": "716", "line": 42, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 42, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "636", "line": 44, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 44, "endColumn": 21}, {"ruleId": "614", "severity": 1, "message": "637", "line": 120, "column": 6, "nodeType": "616", "endLine": 120, "endColumn": 27, "suggestions": "717"}, {"ruleId": "614", "severity": 1, "message": "718", "line": 182, "column": 6, "nodeType": "616", "endLine": 182, "endColumn": 19, "suggestions": "719"}, {"ruleId": "618", "severity": 1, "message": "720", "line": 491, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 491, "endColumn": 25}, {"ruleId": "618", "severity": 1, "message": "721", "line": 529, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 529, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "722", "line": 19, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 19, "endColumn": 15}, {"ruleId": "618", "severity": 1, "message": "723", "line": 24, "column": 24, "nodeType": "620", "messageId": "621", "endLine": 24, "endColumn": 34}, {"ruleId": "614", "severity": 1, "message": "724", "line": 60, "column": 6, "nodeType": "616", "endLine": 60, "endColumn": 21, "suggestions": "725"}, {"ruleId": "614", "severity": 1, "message": "726", "line": 149, "column": 6, "nodeType": "616", "endLine": 149, "endColumn": 8, "suggestions": "727"}, {"ruleId": "614", "severity": 1, "message": "728", "line": 176, "column": 6, "nodeType": "616", "endLine": 176, "endColumn": 41, "suggestions": "729"}, {"ruleId": "618", "severity": 1, "message": "714", "line": 22, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 22, "endColumn": 24}, {"ruleId": "618", "severity": 1, "message": "730", "line": 28, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 28, "endColumn": 18}, {"ruleId": "618", "severity": 1, "message": "731", "line": 29, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 29, "endColumn": 24}, {"ruleId": "618", "severity": 1, "message": "732", "line": 55, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 55, "endColumn": 15}, {"ruleId": "618", "severity": 1, "message": "733", "line": 56, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 56, "endColumn": 12}, {"ruleId": "618", "severity": 1, "message": "734", "line": 58, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 58, "endColumn": 12}, {"ruleId": "618", "severity": 1, "message": "735", "line": 59, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 59, "endColumn": 15}, {"ruleId": "618", "severity": 1, "message": "736", "line": 65, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 65, "endColumn": 22}, {"ruleId": "618", "severity": 1, "message": "737", "line": 68, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 68, "endColumn": 14}, {"ruleId": "618", "severity": 1, "message": "738", "line": 77, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 77, "endColumn": 20}, {"ruleId": "618", "severity": 1, "message": "739", "line": 79, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 79, "endColumn": 18}, {"ruleId": "614", "severity": 1, "message": "692", "line": 99, "column": 6, "nodeType": "616", "endLine": 99, "endColumn": 19, "suggestions": "740"}, {"ruleId": "614", "severity": 1, "message": "741", "line": 285, "column": 6, "nodeType": "616", "endLine": 285, "endColumn": 81, "suggestions": "742"}, {"ruleId": "618", "severity": 1, "message": "743", "line": 287, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 287, "endColumn": 31}, {"ruleId": "618", "severity": 1, "message": "744", "line": 426, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 426, "endColumn": 19}, {"ruleId": "745", "severity": 1, "message": "746", "line": 61, "column": 54, "nodeType": "747", "endLine": 61, "endColumn": 83}, {"ruleId": "745", "severity": 1, "message": "746", "line": 216, "column": 17, "nodeType": "747", "endLine": 216, "endColumn": 57}, {"ruleId": "618", "severity": 1, "message": "748", "line": 11, "column": 30, "nodeType": "620", "messageId": "621", "endLine": 11, "endColumn": 49}, {"ruleId": "618", "severity": 1, "message": "619", "line": 5, "column": 20, "nodeType": "620", "messageId": "621", "endLine": 5, "endColumn": 29}, {"ruleId": "618", "severity": 1, "message": "749", "line": 19, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 19, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "750", "line": 19, "column": 25, "nodeType": "620", "messageId": "621", "endLine": 19, "endColumn": 39}, {"ruleId": "618", "severity": 1, "message": "751", "line": 2, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 2, "endColumn": 20}, {"ruleId": "618", "severity": 1, "message": "752", "line": 3, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 3, "endColumn": 13}, {"ruleId": "618", "severity": 1, "message": "619", "line": 1, "column": 35, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 44}, {"ruleId": "618", "severity": 1, "message": "753", "line": 8, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 8, "endColumn": 35}, {"ruleId": "618", "severity": 1, "message": "754", "line": 8, "column": 37, "nodeType": "620", "messageId": "621", "endLine": 8, "endColumn": 45}, {"ruleId": "618", "severity": 1, "message": "755", "line": 40, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 40, "endColumn": 25}, {"ruleId": "618", "severity": 1, "message": "756", "line": 3, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 3, "endColumn": 14}, {"ruleId": "618", "severity": 1, "message": "757", "line": 21, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 21, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "758", "line": 18, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 18, "endColumn": 24}, {"ruleId": "618", "severity": 1, "message": "759", "line": 18, "column": 26, "nodeType": "620", "messageId": "621", "endLine": 18, "endColumn": 41}, {"ruleId": "618", "severity": 1, "message": "760", "line": 41, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 41, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "761", "line": 41, "column": 25, "nodeType": "620", "messageId": "621", "endLine": 41, "endColumn": 39}, {"ruleId": "614", "severity": 1, "message": "762", "line": 52, "column": 8, "nodeType": "616", "endLine": 52, "endColumn": 21, "suggestions": "763"}, {"ruleId": "618", "severity": 1, "message": "705", "line": 7, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 7, "endColumn": 18}, {"ruleId": "614", "severity": 1, "message": "660", "line": 28, "column": 6, "nodeType": "616", "endLine": 28, "endColumn": 18, "suggestions": "764"}, {"ruleId": "614", "severity": 1, "message": "765", "line": 54, "column": 8, "nodeType": "616", "endLine": 54, "endColumn": 54, "suggestions": "766"}, {"ruleId": "618", "severity": 1, "message": "619", "line": 1, "column": 27, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 36}, {"ruleId": "618", "severity": 1, "message": "767", "line": 12, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 12, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "769", "line": 16, "column": 16, "nodeType": "770", "messageId": "685", "endLine": 16, "endColumn": 19}, {"ruleId": "618", "severity": 1, "message": "771", "line": 27, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 27, "endColumn": 22}, {"ruleId": "618", "severity": 1, "message": "772", "line": 32, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 32, "endColumn": 18}, {"ruleId": "618", "severity": 1, "message": "773", "line": 54, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 54, "endColumn": 18}, {"ruleId": "618", "severity": 1, "message": "774", "line": 57, "column": 5, "nodeType": "620", "messageId": "621", "endLine": 57, "endColumn": 15}, {"ruleId": "614", "severity": 1, "message": "775", "line": 660, "column": 6, "nodeType": "616", "endLine": 660, "endColumn": 117, "suggestions": "776"}, {"ruleId": "614", "severity": 1, "message": "777", "line": 792, "column": 6, "nodeType": "616", "endLine": 792, "endColumn": 44, "suggestions": "778"}, {"ruleId": "614", "severity": 1, "message": "779", "line": 827, "column": 6, "nodeType": "616", "endLine": 827, "endColumn": 19, "suggestions": "780"}, {"ruleId": "618", "severity": 1, "message": "781", "line": 6, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 6, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "619", "line": 1, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 19}, {"ruleId": "618", "severity": 1, "message": "782", "line": 1, "column": 31, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 41}, {"ruleId": "618", "severity": 1, "message": "783", "line": 3, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 3, "endColumn": 19}, {"ruleId": "618", "severity": 1, "message": "784", "line": 4, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 4, "endColumn": 21}, {"ruleId": "618", "severity": 1, "message": "785", "line": 7, "column": 20, "nodeType": "620", "messageId": "621", "endLine": 7, "endColumn": 26}, {"ruleId": "618", "severity": 1, "message": "786", "line": 10, "column": 12, "nodeType": "620", "messageId": "621", "endLine": 10, "endColumn": 25}, {"ruleId": "618", "severity": 1, "message": "787", "line": 6, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 6, "endColumn": 26}, {"ruleId": "618", "severity": 1, "message": "788", "line": 15, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 15, "endColumn": 22}, {"ruleId": "614", "severity": 1, "message": "789", "line": 32, "column": 6, "nodeType": "616", "endLine": 32, "endColumn": 19, "suggestions": "790"}, {"ruleId": "618", "severity": 1, "message": "791", "line": 33, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 33, "endColumn": 18}, {"ruleId": "618", "severity": 1, "message": "792", "line": 98, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 98, "endColumn": 27}, {"ruleId": "618", "severity": 1, "message": "793", "line": 53, "column": 9, "nodeType": "620", "messageId": "621", "endLine": 53, "endColumn": 23}, {"ruleId": "618", "severity": 1, "message": "794", "line": 126, "column": 15, "nodeType": "620", "messageId": "621", "endLine": 126, "endColumn": 19}, {"ruleId": "614", "severity": 1, "message": "795", "line": 141, "column": 6, "nodeType": "616", "endLine": 141, "endColumn": 25, "suggestions": "796"}, {"ruleId": "614", "severity": 1, "message": "797", "line": 251, "column": 6, "nodeType": "616", "endLine": 251, "endColumn": 37, "suggestions": "798"}, {"ruleId": "618", "severity": 1, "message": "799", "line": 292, "column": 15, "nodeType": "620", "messageId": "621", "endLine": 292, "endColumn": 29}, {"ruleId": "618", "severity": 1, "message": "800", "line": 293, "column": 15, "nodeType": "620", "messageId": "621", "endLine": 293, "endColumn": 27}, {"ruleId": "614", "severity": 1, "message": "801", "line": 377, "column": 5, "nodeType": "616", "endLine": 377, "endColumn": 58, "suggestions": "802"}, {"ruleId": "614", "severity": 1, "message": "803", "line": 381, "column": 9, "nodeType": "804", "endLine": 400, "endColumn": 4}, {"ruleId": "614", "severity": 1, "message": "805", "line": 540, "column": 6, "nodeType": "616", "endLine": 540, "endColumn": 82, "suggestions": "806"}, {"ruleId": "614", "severity": 1, "message": "807", "line": 554, "column": 41, "nodeType": "620", "endLine": 554, "endColumn": 48}, {"ruleId": "614", "severity": 1, "message": "808", "line": 51, "column": 5, "nodeType": "616", "endLine": 51, "endColumn": 24, "suggestions": "809"}, {"ruleId": "618", "severity": 1, "message": "810", "line": 1, "column": 56, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 64}, {"ruleId": "618", "severity": 1, "message": "811", "line": 1, "column": 66, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 69}, {"ruleId": "618", "severity": 1, "message": "812", "line": 1, "column": 71, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 75}, {"ruleId": "618", "severity": 1, "message": "813", "line": 6, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 6, "endColumn": 11}, {"ruleId": "618", "severity": 1, "message": "794", "line": 55, "column": 13, "nodeType": "620", "messageId": "621", "endLine": 55, "endColumn": 17}, {"ruleId": "618", "severity": 1, "message": "814", "line": 1, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 1, "endColumn": 13}, {"ruleId": "618", "severity": 1, "message": "815", "line": 2, "column": 8, "nodeType": "620", "messageId": "621", "endLine": 2, "endColumn": 13}, {"ruleId": "614", "severity": 1, "message": "816", "line": 48, "column": 8, "nodeType": "616", "endLine": 48, "endColumn": 28, "suggestions": "817"}, {"ruleId": "618", "severity": 1, "message": "818", "line": 8, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 8, "endColumn": 21}, {"ruleId": "618", "severity": 1, "message": "819", "line": 16, "column": 11, "nodeType": "620", "messageId": "621", "endLine": 16, "endColumn": 24}, {"ruleId": "618", "severity": 1, "message": "820", "line": 16, "column": 45, "nodeType": "620", "messageId": "621", "endLine": 16, "endColumn": 72}, {"ruleId": "618", "severity": 1, "message": "821", "line": 17, "column": 54, "nodeType": "620", "messageId": "621", "endLine": 17, "endColumn": 68}, {"ruleId": "618", "severity": 1, "message": "822", "line": 105, "column": 15, "nodeType": "620", "messageId": "621", "endLine": 105, "endColumn": 18}, {"ruleId": "614", "severity": 1, "message": "823", "line": 441, "column": 6, "nodeType": "616", "endLine": 441, "endColumn": 8, "suggestions": "824"}, {"ruleId": "614", "severity": 1, "message": "825", "line": 483, "column": 6, "nodeType": "616", "endLine": 483, "endColumn": 20, "suggestions": "826"}, {"ruleId": "827", "severity": 1, "message": "828", "line": 73, "column": 25, "nodeType": "747", "endLine": 73, "endColumn": 97}, {"ruleId": "614", "severity": 1, "message": "762", "line": 34, "column": 6, "nodeType": "616", "endLine": 34, "endColumn": 19, "suggestions": "829"}, {"ruleId": "618", "severity": 1, "message": "830", "line": 8, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 8, "endColumn": 29}, {"ruleId": "618", "severity": 1, "message": "831", "line": 37, "column": 21, "nodeType": "620", "messageId": "621", "endLine": 37, "endColumn": 26}, {"ruleId": "614", "severity": 1, "message": "615", "line": 26, "column": 8, "nodeType": "616", "endLine": 26, "endColumn": 10, "suggestions": "832"}, {"ruleId": "618", "severity": 1, "message": "833", "line": 11, "column": 10, "nodeType": "620", "messageId": "621", "endLine": 11, "endColumn": 20}, {"ruleId": "618", "severity": 1, "message": "834", "line": 11, "column": 22, "nodeType": "620", "messageId": "621", "endLine": 11, "endColumn": 35}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAvailableDates'. Either include it or remove the dependency array.", "ArrayExpression", ["835"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'requestPermission'. Either include it or remove the dependency array.", ["836"], "React Hook useEffect has a missing dependency: 'setCallData'. Either include it or remove the dependency array.", ["837"], "React Hook useCallback has unnecessary dependencies: 'showAllReminder' and 'showReminder'. Either exclude them or remove the dependency array.", ["838"], "'Navbar' is defined but never used.", "'chatsLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'graphReport'. Either include it or remove the dependency array.", ["839"], "React Hook useEffect has a missing dependency: 'fetchNewContactList'. Either include it or remove the dependency array.", ["840"], "React Hook useEffect has a missing dependency: 'getWhatsaAppNumberList'. Either include it or remove the dependency array.", ["841"], "'userBalance' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'Balance'. Either include it or remove the dependency array.", ["842"], "'setSelectedOption' is assigned a value but never used.", "'contactLists' is assigned a value but never used.", "'setContactLists' is assigned a value but never used.", "'handleManualAssignmentChange' is assigned a value but never used.", "'addManualAssignment' is assigned a value but never used.", "'removeManualAssignment' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAgent'. Either include it or remove the dependency array.", ["843"], "React Hook useEffect has missing dependencies: 'fetchAgents' and 'fetchTemplate'. Either include them or remove the dependency array.", ["844"], "'Option' is assigned a value but never used.", "'agentList' is defined but never used.", "React Hook useEffect has a missing dependency: 'intial_data'. Either include it or remove the dependency array.", ["845"], "'setCustomerData' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCustomerData'. Either include it or remove the dependency array.", ["846"], "'useCallback' is defined but never used.", "'showDatePicker' is assigned a value but never used.", ["847"], "React Hook useEffect has a missing dependency: 'fetchSearch'. Either include it or remove the dependency array.", ["848"], "'handleDateClick' is assigned a value but never used.", "'clearFilter' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'nextPage' is assigned a value but never used.", "'SlCalender' is defined but never used.", "'currentUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchReportData', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["849"], "React Hook useEffect has a missing dependency: 'fetchReportData'. Either include it or remove the dependency array.", ["850"], "React Hook useEffect has missing dependencies: 'fetchAgentData', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["851"], "React Hook useEffect has a missing dependency: 'fetchAgentData'. Either include it or remove the dependency array.", ["852"], ["853"], "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchkeywords'. Either include it or remove the dependency array.", ["854"], "'CircularProgress' is defined but never used.", "'arrayMove' is defined but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'fetchContacts'. Either include it or remove the dependency array.", ["855"], "React Hook useEffect has a missing dependency: 'fetchAgents'. Either include it or remove the dependency array.", ["856"], "'Card' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaClock' is defined but never used.", "'IoMdSettings' is defined but never used.", "'Link' is defined but never used.", "'agent' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchActivityLog' and 'fetchAgentData'. Either include them or remove the dependency array.", ["857"], "'formatDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'endBreak'. Either include it or remove the dependency array.", ["858"], "'BASE_URL' is defined but never used.", "'setShowDatePicker' is assigned a value but never used.", "'templatePopUp' is assigned a value but never used.", "'setTemplatePopUp' is assigned a value but never used.", "'debouncedSearchInput' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAgents', 'fetchNewContactList', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["859"], "React Hook useEffect has missing dependencies: 'fetchNewContactList' and 'setFetchDataFn'. Either include them or remove the dependency array.", ["860"], "'MdSupportAgent' is defined but never used.", "'permission' is assigned a value but never used.", "'isExpiryClose' is assigned a value but never used.", ["861"], "React Hook useEffect has a missing dependency: 'showAgent'. Either include it or remove the dependency array.", ["862"], "'handleShowMobile' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'toast' is defined but never used.", "'updateUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsOldMsg' and 'setRemainingTime'. Either include them or remove the dependency array.", ["863"], "React Hook useEffect has a missing dependency: 'setKeyBoardOpen'. Either include it or remove the dependency array. If 'setKeyBoardOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["864"], "React Hook useEffect has missing dependencies: 'selectedName' and 'setUserLabels'. Either include them or remove the dependency array. If 'setUserLabels' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["865"], "'BsFilter' is defined but never used.", "'AiOutlineClose' is defined but never used.", "'unReadChat' is assigned a value but never used.", "'setPage' is assigned a value but never used.", "'channel' is assigned a value but never used.", "'setChannel' is assigned a value but never used.", "'setRoleFilterType' is assigned a value but never used.", "'starChats' is assigned a value but never used.", "'setWaitingChats' is assigned a value but never used.", "'filteredChats' is assigned a value but never used.", ["866"], "React Hook useEffect has missing dependencies: 'fetchChats' and 'fetchStarredChats'. Either include them or remove the dependency array.", ["867"], "'getWhatsaAppNumberList' is assigned a value but never used.", "'waitingBtn' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setSelectedCategory' is assigned a value but never used.", "'deleteIndex' is assigned a value but never used.", "'setDeleteIndex' is assigned a value but never used.", "'OverviewCard' is defined but never used.", "'use' is defined but never used.", "'IoIosArrowDroprightCircle' is defined but never used.", "'IoMdMove' is defined but never used.", "'toggleActivities' is assigned a value but never used.", "'Select' is defined but never used.", "'labelOptions' is assigned a value but never used.", "'templateList' is assigned a value but never used.", "'setTemplateList' is assigned a value but never used.", "'countryCode' is assigned a value but never used.", "'setCountryCode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTemplate'. Either include it or remove the dependency array.", ["868"], ["869"], "React Hook useEffect has missing dependencies: 'currentUser.parent_id', 'currentUser.parent_token', and 'selectedMobileNumber'. Either include them or remove the dependency array.", ["870"], "'WaitingCard' is defined but never used.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "'waitingChats' is assigned a value but never used.", "'setUnReadChat' is assigned a value but never used.", "'mobileVisible' is assigned a value but never used.", "'nextConvId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'disable', 'selectedName', 'setAllChats', and 'setUnreadCount'. Either include them or remove the dependency array.", ["871"], "React Hook useEffect has missing dependencies: 'setAllChats', 'setChats', 'setConvPage', 'setSelectedMobileNumber', and 'setSelectedUserDetails'. Either include them or remove the dependency array.", ["872"], "React Hook useEffect has a missing dependency: 'fetchNumberVisibility'. Either include it or remove the dependency array.", ["873"], "'searchInput' is assigned a value but never used.", "'useContext' is defined but never used.", "'ChatState' is defined but never used.", "'AuthContext' is defined but never used.", "'Button' is defined but never used.", "'selected<PERSON><PERSON><PERSON>' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'activeStatus' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setWpProfile'. Either include it or remove the dependency array.", ["874"], "'IOSSwitch' is assigned a value but never used.", "'handleActiveStatus' is assigned a value but never used.", "'DEBOUNCE_DELAY' is assigned a value but never used.", "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'showTypingStatus'. Either include it or remove the dependency array.", ["875"], "React Hook useCallback has a missing dependency: 'languages'. Either include it or remove the dependency array.", ["876"], "'selectionStart' is assigned a value but never used.", "'selectionEnd' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'setText'. Either include it or remove the dependency array.", ["877"], "The 'findCompletedWord' function makes the dependencies of useCallback Hook (at line 522) change on every render. Move it inside the useCallback callback. Alternatively, wrap the definition of 'findCompletedWord' in its own useCallback() Hook.", "VariableDeclarator", "React Hook useCallback has unnecessary dependencies: 'findCurrentWord' and 'translateWordSilently'. Either exclude them or remove the dependency array.", ["878"], "The ref value 'debounceTimeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'debounceTimeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'setActivities'. Either include it or remove the dependency array. If 'setActivities' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["879"], "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'Box' is defined but never used.", "'React' is defined but never used.", "'axios' is defined but never used.", "React Hook useEffect has a missing dependency: 'localSentences'. Either include it or remove the dependency array.", ["880"], "'useDebounce' is defined but never used.", "'saveSentences' is assigned a value but never used.", "'splitParagraphIntoSentences' is assigned a value but never used.", "'setShowPreview' is assigned a value but never used.", "'msg' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'textareaRef'. Either include it or remove the dependency array.", ["881"], "React Hook useEffect has a missing dependency: 'currentUser'. Either include it or remove the dependency array.", ["882"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", ["883"], "'RiArrowDropDownLine' is defined but never used.", "'reply' is assigned a value but never used.", ["884"], "'refreshing' is assigned a value but never used.", "'setRefreshing' is assigned a value but never used.", {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "899", "fix": "900"}, {"desc": "901", "fix": "902"}, {"desc": "903", "fix": "904"}, {"desc": "905", "fix": "906"}, {"desc": "907", "fix": "908"}, {"desc": "909", "fix": "910"}, {"desc": "911", "fix": "912"}, {"desc": "913", "fix": "914"}, {"desc": "915", "fix": "916"}, {"desc": "917", "fix": "918"}, {"desc": "919", "fix": "920"}, {"kind": "921", "justification": "922"}, {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"desc": "927", "fix": "928"}, {"desc": "929", "fix": "930"}, {"desc": "931", "fix": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, {"desc": "937", "fix": "938"}, {"desc": "939", "fix": "940"}, {"desc": "941", "fix": "942"}, {"desc": "943", "fix": "944"}, {"desc": "945", "fix": "946"}, {"desc": "927", "fix": "947"}, {"desc": "948", "fix": "949"}, {"desc": "950", "fix": "951"}, {"desc": "952", "fix": "953"}, {"desc": "954", "fix": "955"}, {"desc": "956", "fix": "957"}, {"desc": "958", "fix": "959"}, {"desc": "960", "fix": "961"}, {"desc": "962", "fix": "963"}, {"desc": "964", "fix": "965"}, {"desc": "966", "fix": "967"}, {"desc": "968", "fix": "969"}, {"desc": "970", "fix": "971"}, {"desc": "972", "fix": "973"}, {"desc": "974", "fix": "975"}, {"desc": "976", "fix": "977"}, {"desc": "978", "fix": "979"}, {"desc": "950", "fix": "980"}, {"desc": "981", "fix": "982"}, "Update the dependencies array to be: [currentUser, fetchAvailableDates]", {"range": "983", "text": "984"}, "Update the dependencies array to be: [currentUser, requestPermission]", {"range": "985", "text": "986"}, "Update the dependencies array to be: [currentUser, setCallData, socket]", {"range": "987", "text": "988"}, "Update the dependencies array to be: [setShowReminder, setShowAllReminder]", {"range": "989", "text": "990"}, "Update the dependencies array to be: [currentUser, graphReport]", {"range": "991", "text": "992"}, "Update the dependencies array to be: [currentUser, fetchNewContactList]", {"range": "993", "text": "994"}, "Update the dependencies array to be: [getWhatsaAppNumberList]", {"range": "995", "text": "996"}, "Update the dependencies array to be: [Balance]", {"range": "997", "text": "998"}, "Update the dependencies array to be: [currentUser, fetchAgent, selectedOption]", {"range": "999", "text": "1000"}, "Update the dependencies array to be: [currentUser, fetchAgents, fetchTemplate]", {"range": "1001", "text": "1002"}, "Update the dependencies array to be: [intial_data]", {"range": "1003", "text": "1004"}, "Update the dependencies array to be: [currentUser, fetchCustomerData, mobile]", {"range": "1005", "text": "1006"}, "Update the dependencies array to be: [currentUser, fetchNewContactList, fromDate, toDate]", {"range": "1007", "text": "1008"}, "Update the dependencies array to be: [debouncedSearchInput, fetchSearch]", {"range": "1009", "text": "1010"}, "Update the dependencies array to be: [currentUser, fetchReportData, fromDate, toDate]", {"range": "1011", "text": "1012"}, "Update the dependencies array to be: [setFetchDataFn, fromDate, toDate, fetchReportData]", {"range": "1013", "text": "1014"}, "Update the dependencies array to be: [currentUser, fetchAgentData, fromDate, toDate]", {"range": "1015", "text": "1016"}, "Update the dependencies array to be: [setFetchDataFn, fromDate, toDate, fetchAgentData]", {"range": "1017", "text": "1018"}, "directive", "", "Update the dependencies array to be: [currentUser, agentId, fetchkeywords]", {"range": "1019", "text": "1020"}, "Update the dependencies array to be: [currentUser, fetchContacts, fromDate, toDate]", {"range": "1021", "text": "1022"}, "Update the dependencies array to be: [currentUser, fetchAgents]", {"range": "1023", "text": "1024"}, "Update the dependencies array to be: [currentUser, agentId, fromDate, toDate, fetchAgentData, fetchActivityLog]", {"range": "1025", "text": "1026"}, "Update the dependencies array to be: [breakInfo, endBreak]", {"range": "1027", "text": "1028"}, "Update the dependencies array to be: [currentUser, fetchAgents, fetchNewContactList, fromDate, toDate]", {"range": "1029", "text": "1030"}, "Update the dependencies array to be: [currentUser, fetchNewContactList, fromDate, setFetchDataFn, toDate]", {"range": "1031", "text": "1032"}, "Update the dependencies array to be: [Balance, currentUser, socket]", {"range": "1033", "text": "1034"}, "Update the dependencies array to be: [currentUser, showAgent]", {"range": "1035", "text": "1036"}, "Update the dependencies array to be: [remainingTime, setIsOldMsg, setRemainingTime]", {"range": "1037", "text": "1038"}, "Update the dependencies array to be: [setKeyBoardOpen]", {"range": "1039", "text": "1040"}, "Update the dependencies array to be: [currentUser, selectedMobileNumber, selectedName, setUserLabels]", {"range": "1041", "text": "1042"}, {"range": "1043", "text": "1024"}, "Update the dependencies array to be: [currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value, fetchStarredChats, fetchChats]", {"range": "1044", "text": "1045"}, "Update the dependencies array to be: [currentUser, fetchTemplate]", {"range": "1046", "text": "1047"}, "Update the dependencies array to be: [fetchSearch, inputValue]", {"range": "1048", "text": "1049"}, "Update the dependencies array to be: [fetchType, fetchAllParams, fetchMobileParams, currentUser.parent_id, currentUser.parent_token, selectedMobileNumber]", {"range": "1050", "text": "1051"}, "Update the dependencies array to be: [currentUser, selectedMobileNumber, allChats, dispatch, unReadChat, data, socket, chatFilterType, chatCategory, disable, selectedName, setAllChats, setUnreadCount]", {"range": "1052", "text": "1053"}, "Update the dependencies array to be: [currentUser, allChats, socket, chats, setAllChats, setChats, setSelectedMobileNumber, setSelectedUserDetails, setConvPage]", {"range": "1054", "text": "1055"}, "Update the dependencies array to be: [currentUser, fetchNumberVisibility]", {"range": "1056", "text": "1057"}, "Update the dependencies array to be: [currentUser, setWpProfile]", {"range": "1058", "text": "1059"}, "Update the dependencies array to be: [text, currentUser, showTypingStatus]", {"range": "1060", "text": "1061"}, "Update the dependencies array to be: [languages, currentUser.parent_id, currentUser.parent_token]", {"range": "1062", "text": "1063"}, "Update the dependencies array to be: [selectedLanguage, isTranslationEnabled, currentUser.parent_id, currentUser.parent_token, setText]", {"range": "1064", "text": "1065"}, "Update the dependencies array to be: [isTranslationEnabled, text?.length]", {"range": "1066", "text": "1067"}, "Update the dependencies array to be: [currentUser, phone, setActivities]", {"range": "1068", "text": "1069"}, "Update the dependencies array to be: [debouncedInputText, localSentences]", {"range": "1070", "text": "1071"}, "Update the dependencies array to be: [textareaRef]", {"range": "1072", "text": "1073"}, "Update the dependencies array to be: [text, socket, currentUser]", {"range": "1074", "text": "1075"}, {"range": "1076", "text": "1047"}, "Update the dependencies array to be: [fetchAvailableDates]", {"range": "1077", "text": "1078"}, [13636, 13649], "[currentUser, fetchAvailableDates]", [2528, 2541], "[currentUser, requestPermission]", [3554, 3575], "[currentUser, setCallData, socket]", [6022, 6090], "[setShowReminder, setShowAllReminder]", [6046, 6059], "[current<PERSON><PERSON>, graphReport]", [6119, 6132], "[current<PERSON><PERSON>, fetchNewContactList]", [7436, 7438], "[getWhatsaAppNumberList]", [1065, 1067], "[Balance]", [6505, 6534], "[currentUser, fetchAgent, selectedOption]", [9719, 9732], "[currentUser, fetchAgents, fetchTemplate]", [1030, 1032], "[intial_data]", [1657, 1678], "[currentUser, fetchCustomerData, mobile]", [3033, 3064], "[current<PERSON><PERSON>, fetchNewContactList, fromDate, toDate]", [5434, 5456], "[debouncedSearchInput, fetchSearch]", [1085, 1098], "[currentUser, fetchReportData, fromDate, toDate]", [1199, 1233], "[setFetchDataFn, fromDate, toDate, fetchReportData]", [951, 964], "[currentUser, fetchAgentData, fromDate, toDate]", [1120, 1154], "[setFetchDataFn, fromDate, toDate, fetchAgentData]", [1369, 1391], "[currentUser, agentId, fetchkeywords]", [8271, 8302], "[currentUser, fetchContacts, fromDate, toDate]", [8363, 8376], "[current<PERSON><PERSON>, fetchAgents]", [3757, 3797], "[currentUser, agentId, fromDate, toDate, fetchAgentData, fetchActivityLog]", [1575, 1586], "[breakInfo, endBreak]", [3486, 3499], "[current<PERSON><PERSON>, fetchA<PERSON>s, fetchNewContactList, fromDate, toDate]", [3609, 3640], "[current<PERSON><PERSON>, fetchNewContactList, fromDate, setFetchDataFn, toDate]", [4176, 4197], "[Balance, currentUser, socket]", [5770, 5783], "[current<PERSON><PERSON>, showAgent]", [2262, 2277], "[remainingTime, setIsOldMsg, setRemainingTime]", [5115, 5117], "[setKeyBoardOpen]", [5843, 5878], "[current<PERSON><PERSON>, selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, setUserLabels]", [3450, 3463], [8686, 8761], "[currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value, fetchStarredChats, fetchChats]", [2220, 2233], "[currentUser, fetchTemplate]", [770, 782], "[fetchSearch, inputValue]", [2221, 2267], "[fetchType, fetchAllParams, fetchMobileParams, currentUser.parent_id, currentUser.parent_token, selectedMobileNumber]", [22321, 22432], "[currentUser, selectedMobileNumber, allChats, dispatch, unReadChat, data, socket, chatFilterType, chatCategory, disable, selectedName, setAllChats, setUnreadCount]", [28281, 28319], "[currentUser, allChats, socket, chats, setAllChats, setChats, setSelectedMobileNumber, setSelectedUserDetails, setConvPage]", [29332, 29345], "[currentUser, fetchNumberVisibility]", [1291, 1304], "[current<PERSON><PERSON>, setWpProfile]", [5003, 5022], "[text, currentUser, showTypingStatus]", [8911, 8942], "[languages, currentUser.parent_id, currentUser.parent_token]", [13373, 13426], "[selectedLanguage, isTranslationEnabled, currentUser.parent_id, currentUser.parent_token, setText]", [18274, 18350], "[isTranslationEnabled, text?.length]", [1613, 1632], "[currentUser, phone, setActivities]", [1892, 1912], "[debouncedInputText, localSentences]", [13754, 13756], "[textareaRef]", [14742, 14756], "[text, socket, currentUser]", [1430, 1443], [845, 847], "[fetchAvailableDates]"]