import React, { useState, useEffect } from 'react';
import { ChatState } from '../../context/AllProviders';
import { getCountryDataWithFlags } from '../../utils/countryFlags';

const OnlineUsersCount = () => {
    const {
        onlineUsersData,
        historicalMode,
        selectedDate,
        historicalData,
        availableDates,
        fetchAvailableDates,
        switchToHistoricalMode,
        switchToRealTimeMode,
        loading
    } = ChatState();

    const [activeTab, setActiveTab] = useState('country');
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [selectedWebsite, setSelectedWebsite] = useState('all');
    const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false);

    // Fetch available dates on component mount
    useEffect(() => {
        fetchAvailableDates();
    }, []);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (showDatePicker && !event.target.closest('.date-picker-container')) {
                setShowDatePicker(false);
            }
            if (showWebsiteDropdown && !event.target.closest('.website-dropdown-container')) {
                setShowWebsiteDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showDatePicker, showWebsiteDropdown]);

    // Determine which data to use based on mode
    const currentData = historicalMode ? historicalData : onlineUsersData;

    // ✅ CRITICAL FIX: Extract website breakdown data and create website options
    const websiteBreakdown = currentData.websiteWidgetBreakdown || [];
    const websiteOptions = [
        { value: 'all', label: 'All Websites', count: currentData.totalOnline || 0 },
        ...websiteBreakdown.map(item => ({
            value: `${item.websiteUrl}|${item.widgetId}`,
            label: item.websiteUrl || 'Unknown Website',
            widgetId: item.widgetId,
            count: item.userCount,
            countries: item.countries || [],
            cities: item.cities || []
        }))
    ];

    // ✅ CRITICAL FIX: Filter data based on selected website
    let filteredData = currentData;
    if (selectedWebsite !== 'all') {
        const selectedBreakdown = websiteBreakdown.find(item =>
            `${item.websiteUrl}|${item.widgetId}` === selectedWebsite
        );
        if (selectedBreakdown) {
            // Create filtered data structure for selected website
            filteredData = {
                ...currentData,
                totalOnline: selectedBreakdown.userCount,
                usersByCountry: selectedBreakdown.countries.map(country => ({
                    country: country,
                    user_count: 1 // This would need to be calculated properly from server
                })),
                usersByCity: selectedBreakdown.cities.map(city => ({
                    city: city,
                    user_count: 1 // This would need to be calculated properly from server
                }))
            };
        }
    }

    const countryWiseData = getCountryDataWithFlags(filteredData.usersByCountry || []);
    const cityWiseData = filteredData.usersByCity || [];
    const totalLiveUsers = filteredData.totalOnline || 0;
    const isConnected = historicalMode ? true : onlineUsersData.isConnected;

    // Helper functions
    const parseCityData = (cityString) => {
        if (!cityString || cityString === 'Unknown') {
            return { city: 'Unknown', country: 'Unknown' };
        }
        
        const parts = cityString.split(', ');
        if (parts.length >= 2) {
            const city = parts[0];
            const country = parts.slice(1).join(', ');
            return { city, country };
        }
        
        return { city: cityString, country: 'Unknown' };
    };

    const getCityAbbreviation = (cityName) => {
        if (!cityName || cityName === 'Unknown') return 'UN';
        
        const cleanedCity = cityName
            .replace(/\b(City|Town|Village|Municipality)\b/gi, '')
            .trim();
        
        const words = cleanedCity.split(' ').filter(word => word.length > 0);
        
        if (words.length >= 2) {
            return (words[0][0] + words[1][0]).toUpperCase();
        } else if (words.length === 1) {
            const word = words[0];
            if (word.length >= 2) {
                return word.substring(0, 2).toUpperCase();
            } else {
                return (word + 'X').toUpperCase();
            }
        }
        
        return 'UN';
    };

    const filteredCityData = cityWiseData.filter(item => {
        const { city } = parseCityData(item.city);
        return city !== 'Unknown';
    });

const handleDateSelect = (dateInfo) => {
    console.log('🔍 Raw dateInfo received:', dateInfo);
    
    // ✅ FIXED: Extract clean date regardless of format
    let cleanDate;
    if (typeof dateInfo === 'string') {
        cleanDate = dateInfo.includes('T') ? dateInfo.split('T')[0] : dateInfo;
    } else if (dateInfo && dateInfo.date) {
        cleanDate = dateInfo.date.includes('T') ? dateInfo.date.split('T')[0] : dateInfo.date;
    } else {
        console.error('❌ Invalid date format received:', dateInfo);
        return;
    }
    
    console.log(`📅 User selected clean date: ${cleanDate}`);
    switchToHistoricalMode(cleanDate);
    setShowDatePicker(false);
};



const formatDate = (dateString) => {
    console.log('🔍 formatDate input:', dateString);
    const date = new Date(dateString);
    console.log('🔍 formatDate parsed date:', date);
    console.log('🔍 formatDate timezone offset:', date.getTimezoneOffset());
    
    const formatted = date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
    
    console.log('🔍 formatDate output:', formatted);
    return formatted;
};


    const formatDateShort = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric' 
        });
    };

    return (
        <div className="bg-white shadow-sm" style={{ 
            borderRadius: "12px", 
            width: "100%", 
            maxWidth: "320px", 
            flexShrink: 0,
            border: "1px solid #e9ecef"
        }}>
            {/* Header */}
            <div className="p-3 border-bottom" style={{ backgroundColor: "#f8f9fa", borderRadius: "12px 12px 0 0" }}>
                <div className="d-flex justify-content-between align-items-center">
                    <h6 className="mb-0 fw-semibold" style={{ color: "#495057" }}>
                        {historicalMode ? 'Historical Users' : 'Live Users'}
                    </h6>
                    <div className="d-flex align-items-center">
                        {!historicalMode && (
                            <>
                                <div className={`rounded-circle me-2 ${isConnected ? 'bg-success' : 'bg-danger'}`}
                                     style={{ width: "8px", height: "8px" }}
                                     title={isConnected ? 'Connected' : 'Disconnected'}>
                                </div>
                                <span className="text-muted" style={{ fontSize: "12px" }}>
                                    {isConnected ? 'Live' : 'Offline'}
                                </span>
                            </>
                        )}
                        {historicalMode && (
                            <span className="text-muted" style={{ fontSize: "12px" }}>
                                {formatDateShort(selectedDate)}
                            </span>
                        )}
                    </div>
                </div>
            </div>

            <div className="p-3">
                {/* Website Filter Dropdown */}
                <div className="mb-3">
                    <div className="position-relative website-dropdown-container">
                        <button
                            className="btn btn-outline-secondary btn-sm w-100 d-flex justify-content-between align-items-center"
                            style={{
                                borderRadius: "8px",
                                fontSize: "12px",
                                fontWeight: "500",
                                padding: "8px 12px",
                                border: "1px solid #dee2e6"
                            }}
                            onClick={() => setShowWebsiteDropdown(!showWebsiteDropdown)}
                            disabled={loading}
                        >
                            <span className="text-truncate">
                                {websiteOptions.find(opt => opt.value === selectedWebsite)?.label || 'All Websites'}
                                {selectedWebsite !== 'all' && (
                                    <span className="text-muted ms-1">
                                        ({websiteOptions.find(opt => opt.value === selectedWebsite)?.count || 0} users)
                                    </span>
                                )}
                            </span>
                            <span style={{ fontSize: "10px" }}>▼</span>
                        </button>

                        {/* Website Dropdown */}
                        {showWebsiteDropdown && !loading && (
                            <div className="position-absolute w-100" style={{
                                top: "100%",
                                left: 0,
                                zIndex: 1000,
                                backgroundColor: "white",
                                border: "1px solid #dee2e6",
                                borderRadius: "8px",
                                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                                maxHeight: "200px",
                                overflowY: "auto",
                                marginTop: "4px"
                            }}>
                                <div className="px-3 py-2 border-bottom" style={{
                                    backgroundColor: "#f8f9fa",
                                    fontSize: "11px",
                                    fontWeight: "600",
                                    color: "#6c757d"
                                }}>
                                    Select Website ({websiteOptions.length} available)
                                </div>

                                {websiteOptions.map((option, index) => (
                                    <div
                                        key={option.value}
                                        className="px-3 py-2"
                                        style={{
                                            cursor: "pointer",
                                            fontSize: "12px",
                                            borderBottom: index < websiteOptions.length - 1 ? "1px solid #f1f3f4" : "none",
                                            backgroundColor: selectedWebsite === option.value ? "#e3f2fd" : "transparent"
                                        }}
                                        onMouseEnter={(e) => {
                                            if (selectedWebsite !== option.value) {
                                                e.target.style.backgroundColor = "#f8f9fa";
                                            }
                                        }}
                                        onMouseLeave={(e) => {
                                            if (selectedWebsite !== option.value) {
                                                e.target.style.backgroundColor = "transparent";
                                            }
                                        }}
                                        onClick={() => {
                                            setSelectedWebsite(option.value);
                                            setShowWebsiteDropdown(false);
                                        }}
                                    >
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="text-truncate">
                                                <div className="fw-semibold">{option.label}</div>
                                                {option.widgetId && (
                                                    <div className="text-muted" style={{ fontSize: "10px" }}>
                                                        Widget: {option.widgetId.substring(0, 8)}...
                                                    </div>
                                                )}
                                            </div>
                                            <div className="d-flex align-items-center">
                                                <span className="badge bg-primary me-2" style={{ fontSize: "10px" }}>
                                                    {option.count}
                                                </span>
                                                {selectedWebsite === option.value && (
                                                    <span style={{ color: "#1976d2", fontSize: "10px" }}>✓</span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                {/* Mode Switch Buttons */}
                <div className="d-flex mb-3" style={{ gap: "8px" }}>
                    <button 
                        className={`btn btn-sm ${!historicalMode ? 'btn-success' : 'btn-outline-success'}`}
                        style={{ 
                            borderRadius: "6px",
                            fontSize: "11px",
                            fontWeight: "600",
                            flex: 1
                        }}
                        onClick={switchToRealTimeMode}
                        disabled={loading}
                    >
                        Live
                    </button>
                    <div className="position-relative date-picker-container" style={{ flex: 1 }}>
                        <button 
                            className={`btn btn-sm w-100 ${historicalMode ? 'btn-primary' : 'btn-outline-primary'}`}
                            style={{ 
                                borderRadius: "6px",
                                fontSize: "11px",
                                fontWeight: "600"
                            }}
                            onClick={() => setShowDatePicker(!showDatePicker)}
                            disabled={loading}
                        >
                            {loading ? 'Loading...' : 'History ▼'}
                        </button>
                        
                        {/* Date Picker Dropdown */}
                        {showDatePicker && !loading && (
                            <div className="position-absolute" style={{
                                top: "100%",
                                left: 0,
                                right: 0,
                                zIndex: 1000,
                                backgroundColor: "white",
                                border: "1px solid #dee2e6",
                                borderRadius: "8px",
                                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                                maxHeight: "250px",
                                overflowY: "auto",
                                marginTop: "4px"
                            }}>
                                <div className="px-3 py-2 border-bottom" style={{
                                    backgroundColor: "#f8f9fa",
                                    fontSize: "11px",
                                    fontWeight: "600",
                                    color: "#6c757d"
                                }}>
                                    Select Date ({availableDates.length} days available)
                                </div>
                                
                                {availableDates.length > 0 ? (
                                    availableDates.map((dateInfo, index) => (

                                        
                                        <div
                                            key={index}
                                            className="px-3 py-2"
                                            style={{
                                                cursor: "pointer",
                                                fontSize: "12px",
                                                borderBottom: index < availableDates.length - 1 ? "1px solid #f1f3f4" : "none",
                                                backgroundColor: selectedDate === dateInfo.date ? "#e3f2fd" : "transparent"
                                            }}
                                            onMouseEnter={(e) => {
                                                if (selectedDate !== dateInfo.date) {
                                                    e.target.style.backgroundColor = "#f8f9fa";
                                                }
                                            }}
                                            onMouseLeave={(e) => {
                                                if (selectedDate !== dateInfo.date) {
                                                    e.target.style.backgroundColor = "transparent";
                                                }
                                            }}
                                            onClick={() => handleDateSelect(dateInfo.date)}
                                        >
                                            <div className="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <div className="fw-semibold">{formatDate(dateInfo.date)}</div>
                                                    <div className="text-muted" style={{ fontSize: "10px" }}>
                                                        {dateInfo.user_count} unique users
                                                    </div>
                                                </div>
                                                {selectedDate === dateInfo.date && (
                                                    <span style={{ color: "#1976d2", fontSize: "10px" }}>✓</span>
                                                )}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="px-3 py-4 text-muted text-center" style={{ fontSize: "12px" }}>
                                        <div>📅</div>
                                        <div className="mt-2">No historical data available</div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>

                {/* Total Users Display */}
                <div className="text-center mb-3">
                    <div className="d-flex justify-content-center align-items-center mb-2">
                        <div className="bg-green" 
                             style={{ 
                                 background: historicalMode 
                                     ? "linear-gradient(135deg, #28a745, #20c997)" 
                                     : "linear-gradient(135deg, #28a745, #20c997)",
                                 borderRadius: "50%",
                                 width: "60px", 
                                 height: "60px",
                                 display: "flex",
                                 justifyContent: "center",
                                 alignItems: "center",
                                 boxShadow: historicalMode 
                                     ? "0 4px 8px rgba(111, 66, 193, 0.3)"
                                     : "0 4px 8px rgba(40, 167, 69, 0.3)",
                                 transition: "all 0.3s ease"
                             }}>
                            <span className="text-white fw-bold fs-4">
                                {loading ? '...' : totalLiveUsers}
                            </span>
                        </div>
                    </div>
                    <p className="text-muted mb-0" style={{ fontSize: "13px", fontWeight: "500" }}>
                        {historicalMode 
                            ? (selectedDate ? `Users on ${formatDate(selectedDate)}` : 'Historical Users')
                            : 'Total Active Users'
                        }
                    </p>
                </div>

                {/* Tab Navigation */}
                <div className="d-flex mb-3" style={{ backgroundColor: "#f8f9fa", borderRadius: "8px", padding: "4px" }}>
                    <button 
                        className={`flex-1 btn btn-sm ${activeTab === 'country' ? 'btn-primary' : 'btn-light'}`}
                        style={{ 
                            borderRadius: "6px",
                            fontSize: "12px",
                            fontWeight: "600",
                            border: "none",
                            transition: "all 0.2s"
                        }}
                        onClick={() => setActiveTab('country')}
                        disabled={loading}
                    >
                        Countries
                    </button>
                    <button 
                        className={`flex-1 btn btn-sm ${activeTab === 'city' ? 'btn-primary' : 'btn-light'}`}
                        style={{ 
                            borderRadius: "6px",
                            fontSize: "12px",
                            fontWeight: "600",
                            border: "none",
                            marginLeft: "4px",
                            transition: "all 0.2s"
                        }}
                        onClick={() => setActiveTab('city')}
                        disabled={loading}
                    >
                        Cities
                    </button>
                </div>

                {/* Content Area */}
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                    {loading ? (
                        <div className="text-center py-4">
                            <div className="spinner-border spinner-border-sm text-primary" role="status" style={{ fontSize: "12px" }}>
                                <span className="visually-hidden">Loading...</span>
                            </div>
                            <div className="text-muted mt-2" style={{ fontSize: "12px" }}>
                                Loading {historicalMode ? 'historical' : 'live'} data...
                            </div>
                        </div>
                    ) : (
                        <>
                            {activeTab === 'country' ? (
                                <div>
                                    {countryWiseData.length > 0 ? (
                                        countryWiseData.map((item, index) => (
                                            <div key={index} 
                                                 className="d-flex justify-content-between align-items-center mb-2 p-2"
                                                 style={{
                                                     backgroundColor: "#fff",
                                                     borderRadius: "8px",
                                                     border: "1px solid #e9ecef",
                                                     transition: "all 0.2s",
                                                     cursor: "pointer"
                                                 }}
                                                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                                                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#fff"}>
                                                <div className="d-flex align-items-center">
                                                    <span className="me-2" style={{ fontSize: "18px" }}>{item.flag}</span>
                                                    <div>
                                                        <div style={{ fontSize: "13px", fontWeight: "600", color: "#495057" }}>
                                                            {item.country}
                                                        </div>
                                                        <div style={{ fontSize: "11px", color: "#6c757d" }}>
                                                            {totalLiveUsers > 0 ? ((item.user_count / totalLiveUsers) * 100).toFixed(1) : 0}%
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="d-flex align-items-center">
                                                    <span className="badge bg-primary me-2" style={{ fontSize: "11px" }}>
                                                        {item.user_count}
                                                    </span>
                                                    <div className="progress" style={{ width: "40px", height: "4px" }}>
                                                        <div className="progress-bar bg-primary"
                                                             style={{
                                                                 width: totalLiveUsers > 0 ? `${(item.user_count / totalLiveUsers) * 100}%` : '0%',
                                                                 borderRadius: "2px"
                                                             }}>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <div className="text-center text-muted py-3" style={{ fontSize: "13px" }}>
                                            {historicalMode 
                                                ? (selectedDate ? `No country data for ${formatDate(selectedDate)}` : 'No historical country data') 
                                                : (isConnected ? 'No country data available' : 'Connecting...')
                                            }
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div>
                                    {filteredCityData.length > 0 ? (
                                        filteredCityData.map((item, index) => {
                                            const { city, country } = parseCityData(item.city);
                                            const cityAbbr = getCityAbbreviation(city);
                                            return (
                                                <div key={index} 
                                                     className="d-flex justify-content-between align-items-center mb-2 p-2"
                                                     style={{
                                                         backgroundColor: "#fff",
                                                         borderRadius: "8px",
                                                         border: "1px solid #e9ecef",
                                                         transition: "all 0.2s",
                                                         cursor: "pointer"
                                                     }}
                                                     onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                                                     onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#fff"}>
                                                    <div className="d-flex align-items-center">
                                                        <div className="me-2" style={{
                                                            width: "20px",
                                                            height: "20px",
                                                            backgroundColor: "#e9ecef",
                                                            borderRadius: "50%",
                                                            display: "flex",
                                                            alignItems: "center",
                                                            justifyContent: "center"
                                                        }}>
                                                            <span style={{ 
                                                                fontSize: "8px", 
                                                                fontWeight: "bold", 
                                                                color: "#6c757d",
                                                                letterSpacing: "-0.5px"
                                                            }}>
                                                                {cityAbbr}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <div style={{ fontSize: "13px", fontWeight: "600", color: "#495057" }}>
                                                                {city}
                                                            </div>
                                                            <div style={{ fontSize: "11px", color: "#6c757d" }}>
                                                                {country} • {totalLiveUsers > 0 ? ((item.user_count / totalLiveUsers) * 100).toFixed(1) : 0}%
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="d-flex align-items-center">
                                                        <span className="badge bg-primary me-2" style={{ fontSize: "11px" }}>
                                                            {item.user_count}
                                                        </span>
                                                        <div className="progress" style={{ width: "40px", height: "4px" }}>
                                                            <div className="progress-bar bg-primary"
                                                                 style={{
                                                                     width: totalLiveUsers > 0 ? `${(item.user_count / totalLiveUsers) * 100}%` : '0%',
                                                                     borderRadius: "2px"
                                                                 }}>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    ) : (
                                        <div className="text-center text-muted py-3" style={{ fontSize: "13px" }}>
                                            {historicalMode 
                                                ? (selectedDate ? `No city data for ${formatDate(selectedDate)}` : 'No historical city data') 
                                                : (isConnected ? 'No city data available' : 'Connecting...')
                                            }
                                        </div>
                                    )}
                                </div>
                            )}
                        </>
                    )}
                </div>

                {/* Footer Stats */}
                <div className="mt-3 pt-2 border-top">
                    <div className="d-flex justify-content-between text-muted" style={{ fontSize: "11px" }}>
                        <span>{countryWiseData.length} Countries</span>
                        <span>{filteredCityData.length} Cities</span>
                        <span>{historicalMode ? 'Historical' : 'Live Updates'}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default OnlineUsersCount;
