const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
const WebSocket = require('ws');
const http = require('http');

const app = express();

const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'usertracking',
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// Ensure tables have website_url and widget_id columns
async function verifySchema() {
    try {
        await pool.execute(`
            ALTER TABLE analytics_events
            ADD COLUMN IF NOT EXISTS website_url VARCHAR(255),
            ADD COLUMN IF NOT EXISTS widget_id VARCHAR(50)
        `);
        await pool.execute(`
            ALTER TABLE location_stats
            ADD COLUMN IF NOT EXISTS website_url VARCHAR(255),
            ADD COLUMN IF NOT EXISTS widget_id VARCHAR(50)
        `);
        console.log('✅ Verified database schema');
    } catch (error) {
        console.error('❌ Schema verification error:', error);
    }
}

verifySchema();

app.use(express.json());
app.use(cors({ origin: '*' }));

const server = http.createServer(app);
const wss = new WebSocket.Server({ port: 8080 });

// ✅ FIXED: Enhanced tracking maps with proper cleanup
const activeUsers = new Map();
const activeSessions = new Map();
const dashboardClients = new Set();
const pageViews = new Map(); // Track current page views (live users on pages)
const todayEvents = new Map(); // ✅ NEW: Track today's events separately
const LOCATIONIQ_TOKEN = '***********************************';

const locationCache = new Map();
const pendingRequests = new Map();
let lastApiCall = 0;
const API_RATE_LIMIT = 1000;

console.log('🚀 Enhanced Analytics WebSocket server running on port 8080');

// ✅ CRITICAL FIX: Helper function to create composite key for tracking
function createCompositeKey(userId, websiteUrl, widgetId) {
    // Create a unique key combining userId, website URL, and widget ID
    const cleanWebsiteUrl = websiteUrl || 'unknown';
    const cleanWidgetId = widgetId || 'unknown';
    return `${userId}|${cleanWebsiteUrl}|${cleanWidgetId}`;
}

// ✅ CRITICAL FIX: Helper function to create page key for tracking
function createPageKey(path, websiteUrl, widgetId) {
    const cleanWebsiteUrl = websiteUrl || 'unknown';
    const cleanWidgetId = widgetId || 'unknown';
    return `${path}|${cleanWebsiteUrl}|${cleanWidgetId}`;
}

// ✅ NEW: Daily data cleanup function
function cleanupDailyData() {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayTimestamp = todayStart.getTime();
    
    console.log('🧹 Cleaning up daily data...');
    
    // Clean up old sessions (keep only today's)
    activeSessions.forEach((session, sessionId) => {
        if (session.sessionStart < todayTimestamp) {
            activeSessions.delete(sessionId);
        }
    });
    
    // Clear today's events for fresh start
    todayEvents.clear();
    
    console.log(`✅ Cleanup complete. Active sessions: ${activeSessions.size}, Today events: ${todayEvents.size}`);
}

// ✅ NEW: Run cleanup at midnight
setInterval(() => {
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0) {
        cleanupDailyData();
    }
}, 60000); // Check every minute

// ✅ FIXED: WebSocket connection handler with proper user tracking
wss.on('connection', (ws) => {
    let userId = null;
    let sessionId = null;
    let websiteUrl = null;
    let widgetId = null;
    let isDashboard = false;
    
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message);
            console.log(`📊 [ANALYTICS] Received:`, data.type, data.eventType || '');
            
            if (data.type === 'user_online') {
                // ✅ CRITICAL FIX: Set userId and sessionId for legacy connections
                userId = data.userId;
                sessionId = data.sessionId || data.userId; // Use userId as fallback for sessionId
                console.log(`👤 [CONNECTION] Set userId: ${userId}, sessionId: ${sessionId}`);
                await handleLegacyUserOnline(ws, data);
                return;
            }
            
            if (data.type === 'analytics_event') {
                // ✅ CRITICAL FIX: Set userId, sessionId, websiteUrl, and widgetId for analytics connections
                if (!userId) userId = data.userId;
                if (!sessionId) sessionId = data.sessionId;
                if (!websiteUrl) websiteUrl = data.websiteUrl;
                if (!widgetId) widgetId = data.widgetId;
                console.log(`📊 [CONNECTION] Set userId: ${userId}, sessionId: ${sessionId}, websiteUrl: ${websiteUrl}, widgetId: ${widgetId}`);
                await handleAnalyticsEvent(ws, data);
                return;
            }
            
            if (data.type === 'dashboard_connect') {
                isDashboard = true;
                dashboardClients.add(ws);
                console.log('📊 Dashboard connected');
                await sendDashboardData(ws);
                return;
            }
                        
        } catch (error) {
            console.error('❌ [ANALYTICS] Message processing error:', error);
        }
    });
    
    // ✅ ENHANCED: Improved disconnect handling with better cleanup
    ws.on('close', () => {
        console.log(`🔌 [DISCONNECT] WebSocket closed. userId: ${userId}, sessionId: ${sessionId}, isDashboard: ${isDashboard}`);
        
        // ✅ CRITICAL FIX: Clean up user data using composite keys
        if (userId && sessionId && websiteUrl && widgetId) {
            console.log(`👤 [CLEANUP] Removing user ${userId} from active users (${websiteUrl}|${widgetId})`);

            // Create composite keys for cleanup
            const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
            const sessionCompositeKey = createCompositeKey(sessionId, websiteUrl, widgetId);

            // Remove from active users
            if (activeUsers.has(userCompositeKey)) {
                activeUsers.delete(userCompositeKey);
                console.log(`✅ [CLEANUP] Removed ${userCompositeKey} from activeUsers. Remaining: ${activeUsers.size}`);
            }

            // Remove from active sessions
            if (activeSessions.has(sessionCompositeKey)) {
                activeSessions.delete(sessionCompositeKey);
                console.log(`✅ [CLEANUP] Removed ${sessionCompositeKey} from activeSessions. Remaining: ${activeSessions.size}`);
            }
            
            // ✅ CRITICAL FIX: Remove from page views using composite keys
            let pagesCleanedUp = 0;
            pageViews.forEach((users, pageCompositeKey) => {
                if (users.has(userCompositeKey)) {
                    users.delete(userCompositeKey);
                    pagesCleanedUp++;
                    console.log(`✅ [CLEANUP] Removed ${userCompositeKey} from page: ${pageCompositeKey}`);

                    // Remove empty page entries
                    if (users.size === 0) {
                        pageViews.delete(pageCompositeKey);
                        console.log(`✅ [CLEANUP] Removed empty page entry: ${pageCompositeKey}`);
                    }
                }
            });

            console.log(`📊 [CLEANUP] Cleaned up ${pagesCleanedUp} page entries for user ${userCompositeKey}`);
            console.log(`📊 [CLEANUP] Current stats - Users: ${activeUsers.size}, Sessions: ${activeSessions.size}, Pages: ${pageViews.size}`);
            
            // Broadcast updated counts
            broadcastUserCount();
            broadcastDashboardData();
        }
        // ✅ FIX 2: Alternative cleanup - find user by WebSocket connection
        else {
            console.log(`🔍 [CLEANUP] No userId found, searching by WebSocket connection...`);
            let foundUserId = null;
            
            // Search through active users to find the one with this WebSocket
            activeUsers.forEach((userData, id) => {
                if (userData.ws === ws) {
                    foundUserId = id;
                    console.log(`🔍 [CLEANUP] Found user by WebSocket: ${id}`);
                }
            });
            
            if (foundUserId) {
                // Clean up using found userId
                activeUsers.delete(foundUserId);
                console.log(`✅ [CLEANUP] Removed user found by WS: ${foundUserId}`);
                
                // Clean up sessions
                activeSessions.forEach((session, sessionId) => {
                    if (session.userId === foundUserId) {
                        activeSessions.delete(sessionId);
                        console.log(`✅ [CLEANUP] Removed session for user: ${foundUserId}`);
                    }
                });
                
                // Clean up page views
                pageViews.forEach((users, page) => {
                    if (users.has(foundUserId)) {
                        users.delete(foundUserId);
                        if (users.size === 0) {
                            pageViews.delete(page);
                        }
                    }
                });
                
                console.log(`📊 [CLEANUP] Backup cleanup completed for ${foundUserId}`);
                broadcastUserCount();
                broadcastDashboardData();
            } else {
                console.log(`❌ [CLEANUP] No user found to clean up for this WebSocket`);
            }
        }
        
        // Clean up dashboard clients
        if (isDashboard) {
            dashboardClients.delete(ws);
            console.log('📊 [CLEANUP] Dashboard client removed');
        }
    });
    
    // ✅ NEW: Handle WebSocket errors
    ws.on('error', (error) => {
        console.error(`❌ [WEBSOCKET ERROR] User: ${userId}, Error:`, error);
        // The close event will still fire after an error, so cleanup will happen there
    });
});


// ✅ FIXED: Handle analytics events with ACTIVE TIME validation
async function handleAnalyticsEvent(ws, data) {
    const { userId, sessionId, eventType, timestamp, url, location, websiteUrl, widgetId } = data;

    // ✅ CRITICAL FIX: Create composite keys for proper tracking
    const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
    const sessionCompositeKey = createCompositeKey(sessionId, websiteUrl, widgetId);

    // Update active user tracking with composite key
    if (!activeUsers.has(userCompositeKey)) {
        activeUsers.set(userCompositeKey, {
            ws: ws,
            userId: userId,
            sessionId: sessionId,
            websiteUrl: websiteUrl,
            widgetId: widgetId,
            lastSeen: timestamp,
            country: 'Unknown',
            city: 'Unknown',
            currentUrl: url,
            isActive: true,
            pageStartTime: timestamp, // ✅ NEW: Track when user actually started viewing
            totalActiveTime: 0 // ✅ NEW: Track only active engagement time
        });
    }

    if (!activeSessions.has(sessionCompositeKey)) {
        activeSessions.set(sessionCompositeKey, {
            userId: userId,
            websiteUrl: websiteUrl,
            widgetId: widgetId,
            sessionStart: timestamp,
            lastActivity: timestamp,
            pageViews: [],
            events: [],
            totalEngagementTime: 0 // ✅ NEW: Track only engagement time
        });
    }
    
    const userData = activeUsers.get(userCompositeKey);
    const sessionData = activeSessions.get(sessionCompositeKey);
    
    userData.lastSeen = timestamp;
    sessionData.lastActivity = timestamp;
    
    // ✅ CRITICAL FIX: Only track ACTIVE engagement time
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayTimestamp = todayStart.getTime();
    
    if (timestamp >= todayTimestamp) {
        // ✅ NEW: Validate and cap timeOnPage based on realistic engagement
        let validatedTimeOnPage = data.timeOnPage || 0;
        
        // Cap maximum time to 10 minutes per page view (realistic reading time)
        const MAX_ENGAGEMENT_TIME = 10 * 60 * 1000; // 10 minutes
        
        // ✅ CRITICAL: If timeOnPage seems unrealistic, calculate based on activity
        if (validatedTimeOnPage > MAX_ENGAGEMENT_TIME || validatedTimeOnPage < 0) {
            // Calculate realistic time based on user activity patterns
            const timeSincePageStart = timestamp - (userData.pageStartTime || timestamp);
            validatedTimeOnPage = Math.min(timeSincePageStart, MAX_ENGAGEMENT_TIME);
            
            console.log(`⚠️ [TIME VALIDATION] Adjusted unrealistic time: ${data.timeOnPage}ms -> ${validatedTimeOnPage}ms for ${userId}`);
        }
        
        // ✅ NEW: Only store reasonable engagement times (1 second to 10 minutes)
        if (validatedTimeOnPage >= 1000 && validatedTimeOnPage <= MAX_ENGAGEMENT_TIME) {
            const eventKey = `${eventType}_${userId}_${Date.now()}`;
            todayEvents.set(eventKey, {
                userId,
                sessionId,
                eventType,
                timestamp,
                url,
                path: data.path,
                title: data.title,
                timeOnPage: validatedTimeOnPage, // ✅ FIXED: Use validated engagement time
                scrollDepth: data.scrollDepth
            });
            
            // ✅ NEW: Update total engagement time
            sessionData.totalEngagementTime += validatedTimeOnPage;
            
            console.log(`✅ [ENGAGEMENT] Recorded ${Math.round(validatedTimeOnPage/1000)}s engagement for ${eventType} on ${data.path}`);
        } else {
            console.log(`❌ [TIME REJECTED] Invalid engagement time: ${validatedTimeOnPage}ms for ${userId} on ${data.path}`);
        }
    }
    
    // Handle different event types
    switch (eventType) {
        case 'page_view':
        case 'page_enter':
            // ✅ NEW: Reset page start time for new page views
        userData.pageStartTime = timestamp;
            await handlePageView(data, userData, sessionData);
            await storeAnalyticsEvent(data);
            break;

        case 'page_exit':
            await handlePageExit(data, userData, sessionData);
            await storeAnalyticsEvent(data);
            break;

        // ✅ REMOVED: location_update events are no longer processed
        // case 'location_update':
        //     if (location) {
        //         await processLocationData(userId, data);
        //     }
        //     break;

        // ✅ REMOVED: click events are no longer processed
        // case 'click':
        case 'widget_open':
        case 'widget_close':
        case 'whatsapp_send_click': // ✅ FIXED: Ensure this event type is handled
            await handleUserInteraction(data, userData, sessionData);
            await storeAnalyticsEvent(data);
            break;

        case 'location_update':
            if (location) {
                await processLocationData(userId, data);
            }
            // Skip database storage for location_update
            break;
            
        case 'page_enter':
            // Process page enter but skip database storage
            await handlePageView(data, userData, sessionData);
            break;
            
        // Ignore other non-essential events
        case 'page_focus':
        case 'page_blur':
        case 'click':
        case 'widget_auto_open':
            console.log(`⚠️ [EVENT IGNORED] Ignoring unnecessary event: ${eventType}`);
            return;

        default:
            console.log(`⚠️ [EVENT WARNING] Unknown event type: ${eventType}`);
            return; // Don't process unknown events
    }

    broadcastUserCount();
    broadcastDashboardData();
}



// ✅ FIXED: Ensure events are only fired once per actual page view
async function handlePageView(data, userData, sessionData) {
    const { url, path, title, location } = data;

    // ✅ NEW: Prevent duplicate page view tracking
    const lastPageView = sessionData.pageViews[sessionData.pageViews.length - 1];
    if (lastPageView && lastPageView.path === path &&
        (data.timestamp - lastPageView.timestamp) < 1000) { // Less than 1 second ago
        console.log(`🚫 [DUPLICATE BLOCKED] Duplicate page view for ${path} within 1 second`);
        return; // Don't record duplicate
    }

    userData.currentUrl = url;

    // ✅ NEW: Process location data if available (for city/country calculation)
    if (location && location.latitude && location.longitude) {
        console.log(`🌍 [PAGE VIEW] Processing location data for ${userData.userId}`);
        await processLocationData(userData.userId, data);
    }

    // ✅ CRITICAL FIX: Track live users on pages using composite key (not cumulative)
    const pageCompositeKey = createPageKey(path, userData.websiteUrl, userData.widgetId);
    if (!pageViews.has(pageCompositeKey)) {
        pageViews.set(pageCompositeKey, new Set());
    }
    const userCompositeKey = createCompositeKey(userData.userId, userData.websiteUrl, userData.widgetId);
    pageViews.get(pageCompositeKey).add(userCompositeKey);

    // Add to session page views with duplicate prevention
    sessionData.pageViews.push({
        url: url,
        path: path,
        title: title,
        timestamp: data.timestamp,
        timeOnPage: data.timeOnPage || 0,
        scrollDepth: data.scrollDepth || 0
    });

    console.log(`📄 [PAGE VIEW] Recorded: ${userData.userId} -> ${path} (Total views: ${sessionData.pageViews.length})`);
}


// ✅ FIXED: Handle page exits - remove from live tracking
async function handlePageExit(data, userData, sessionData) {
    const { path, timeOnPage, scrollDepth } = data;

    // ✅ CRITICAL FIX: Remove from live page views using composite keys
    const pageCompositeKey = createPageKey(path, userData.websiteUrl, userData.widgetId);
    const userCompositeKey = createCompositeKey(userData.userId, userData.websiteUrl, userData.widgetId);

    if (pageViews.has(pageCompositeKey)) {
        pageViews.get(pageCompositeKey).delete(userCompositeKey);
        if (pageViews.get(pageCompositeKey).size === 0) {
            pageViews.delete(pageCompositeKey);
        }
    }
    
    console.log(`📤 [ANALYTICS] Page exit: ${userData.userId} from ${path} (${timeOnPage}ms, ${scrollDepth}% scroll)`);
}

// ✅ FIXED: Enhanced user interaction tracking with all event types
async function handleUserInteraction(data, userData, sessionData) {
    const { eventType, eventData } = data;
    
    // ✅ NEW: Map of recognized interaction events
    const INTERACTION_EVENTS = {
        'whatsapp_send_click': 'WhatsApp Click', // ✅ FIXED: Ensure this is tracked
        'widget_open': 'Widget Opened',         // ✅ FIXED: Ensure this is tracked
        'widget_close': 'Widget Closed',
        'button_click': 'Button Click',
    };
    
    // ✅ NEW: Only track recognized interaction events
    if (!INTERACTION_EVENTS[eventType]) {
        console.log(`⚠️ [EVENT WARNING] Unrecognized event type: ${eventType}`);
        return;
    }
    
    // Add to session events with enhanced data
    const interactionEvent = {
        eventType: eventType,
        eventName: INTERACTION_EVENTS[eventType],
        timestamp: data.timestamp,
        eventData: eventData,
        url: data.url,
        path: data.path
    };
    
    sessionData.events.push(interactionEvent);
    
    // ✅ NEW: Also track in today's events for top interactions
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayTimestamp = todayStart.getTime();
    
    if (data.timestamp >= todayTimestamp) {
        const eventKey = `interaction_${eventType}_${userData.userId}_${Date.now()}`;
        todayEvents.set(eventKey, {
            userId: userData.userId,
            sessionId: userData.sessionId,
            eventType: eventType,
            timestamp: data.timestamp,
            url: data.url,
            path: data.path,
            eventData: eventData
        });
    }
    
    console.log(`🔄 [INTERACTION] ${INTERACTION_EVENTS[eventType]}: ${userData.userId} -> ${eventType}`, eventData);
}



// ✅ FIXED: Comprehensive real-time analytics with PROPER time calculations
function getRealTimeAnalytics() {
    const totalUsers = activeUsers.size;
    const totalSessions = activeSessions.size;
    
    // ✅ FIXED: Top pages shows LIVE users on pages (not cumulative views)
    const topPages = Array.from(pageViews.entries())
        .map(([path, users]) => ({
            path: path,
            users: users.size
        }))
        .sort((a, b) => b.users - a.users)
        .slice(0, 10);
    
    // Calculate active vs idle users
    const now = Date.now();
    let activeCount = 0;
    let idleCount = 0;
    
    activeUsers.forEach((user) => {
        const timeSinceLastActivity = now - user.lastSeen;
        if (timeSinceLastActivity < 60000) {
            activeCount++;
        } else {
            idleCount++;
        }
    });

    // ✅ FIXED: Calculate TODAY's stats from today's events only
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayTimestamp = todayStart.getTime();

    // Filter today's events
    const todayEventsArray = Array.from(todayEvents.values()).filter(event => 
        event.timestamp >= todayTimestamp
    );
    
    // Count unique users for today
    const todayUniqueUsers = new Set(todayEventsArray.map(event => event.userId)).size;
    
    // Count total events for today
    const todayTotalEvents = todayEventsArray.length;
    
    // Count unique sessions for today
    const todayUniqueSessions = new Set(todayEventsArray.map(event => event.sessionId)).size;
    
    // ✅ FIXED: Calculate avg time and scroll with PROPER time limits
    const todayPageViews = todayEventsArray.filter(event => 
        event.eventType === 'page_view' || event.eventType === 'page_enter'
    );
    
    // ✅ CRITICAL FIX: Cap maximum time on page to 30 minutes (1800000ms) to avoid inflated times
    const MAX_TIME_ON_PAGE = 30 * 60 * 1000; // 30 minutes
    
    const validTimeValues = todayPageViews
        .map(event => {
            const timeValue = event.timeOnPage || 0;
            // Cap the time to maximum reasonable value
            return Math.min(timeValue, MAX_TIME_ON_PAGE);
        })
        .filter(time => time > 0 && time < MAX_TIME_ON_PAGE); // Only include reasonable times
    
    const avgTimeOnPage = validTimeValues.length > 0 
        ? validTimeValues.reduce((acc, time) => acc + time, 0) / validTimeValues.length
        : 0;
    
    console.log('🕒 [TIME DEBUG] Time calculation:', {
        todayPageViewsCount: todayPageViews.length,
        validTimeValuesCount: validTimeValues.length,
        validTimeValues: validTimeValues,
        avgTimeOnPage: avgTimeOnPage,
        avgTimeInSeconds: Math.round(avgTimeOnPage / 1000)
    });
    
    const avgScrollDepth = todayPageViews.length > 0
        ? todayPageViews.reduce((acc, event) => acc + (event.scrollDepth || 0), 0) / todayPageViews.length
        : 0;

    // ✅ FIXED: Calculate page performance with PROPER time limits
    const pagePerformance = new Map();
    todayEventsArray.forEach(event => {
        if (event.eventType === 'page_view' || event.eventType === 'page_enter') {
            const path = event.path;
            if (!pagePerformance.has(path)) {
                pagePerformance.set(path, {
                    path: path,
                    title: event.title,
                    unique_users: new Set(),
                    page_views: 0,
                    total_time: 0,
                    total_scroll: 0,
                    unique_sessions: new Set(),
                    valid_time_entries: 0 // ✅ NEW: Track valid time entries
                });
            }
            const page = pagePerformance.get(path);
            page.unique_users.add(event.userId);
            page.page_views++;
            
            // ✅ CRITICAL FIX: Only add time if it's reasonable (less than 30 minutes)
            const timeOnPage = event.timeOnPage || 0;
            if (timeOnPage > 0 && timeOnPage < MAX_TIME_ON_PAGE) {
                page.total_time += timeOnPage;
                page.valid_time_entries++;
            }
            
            page.total_scroll += event.scrollDepth || 0;
            page.unique_sessions.add(event.sessionId);
        }
    });

    const pageStats = Array.from(pagePerformance.entries()).map(([path, data]) => ({
        path: data.path,
        title: data.title,
        unique_users: data.unique_users.size,
        page_views: data.page_views,
        // ✅ FIXED: Use valid time entries for average calculation
        avg_time_on_page: data.valid_time_entries > 0 ? data.total_time / data.valid_time_entries : 0,
        avg_scroll_depth: data.page_views > 0 ? data.total_scroll / data.page_views : 0,
        unique_sessions: data.unique_sessions.size
    })).sort((a, b) => b.unique_users - a.unique_users);

    // ✅ FIXED: Calculate behavior analytics with PROPER session duration limits
    const MAX_SESSION_DURATION = 4 * 60 * 60 * 1000; // 4 hours max session
    
    const todaySessions = Array.from(activeSessions.entries())
        .filter(([sessionId, session]) => session.sessionStart >= todayTimestamp)
        .map(([sessionId, session]) => {
            const rawDuration = session.lastActivity - session.sessionStart;
            const cappedDuration = Math.min(rawDuration, MAX_SESSION_DURATION);
            
            return {
                session_id: sessionId,
                user_id: session.userId,
                session_start: session.sessionStart,
                session_end: session.lastActivity,
                session_duration: cappedDuration, // ✅ FIXED: Capped duration
                events_count: session.events.length,
                pages_visited: session.pageViews.length
            };
        });

    // Count event types from today's events
    const eventTypeCounts = new Map();
    todayEventsArray.forEach(event => {
        eventTypeCounts.set(event.eventType, (eventTypeCounts.get(event.eventType) || 0) + 1);
    });

    const interactions = Array.from(eventTypeCounts.entries()).map(([eventType, count]) => {
        const uniqueUsers = new Set(
            todayEventsArray
                .filter(event => event.eventType === eventType)
                .map(event => event.userId)
        ).size;
        
        return {
            event_type: eventType,
            count: count,
            unique_users: uniqueUsers
        };
    }).sort((a, b) => b.count - a.count);
    
    console.log('📊 [SERVER] FIXED Analytics calculated:', {
        totalUsers,
        totalSessions,
        activeCount,
        todayUniqueUsers,
        todayTotalEvents,
        todayUniqueSessions,
        avgTimeOnPageSeconds: Math.round(avgTimeOnPage / 1000),
        pageStatsCount: pageStats.length,
        interactionsCount: interactions.length,
        liveTopPagesCount: topPages.length
    });
    
    // ✅ CRITICAL FIX: Add breakdown by website and widget
    const websiteWidgetBreakdown = new Map();
    activeUsers.forEach((userData, compositeKey) => {
        const websiteUrl = userData.websiteUrl || 'unknown';
        const widgetId = userData.widgetId || 'unknown';
        const key = `${websiteUrl}|${widgetId}`;

        if (!websiteWidgetBreakdown.has(key)) {
            websiteWidgetBreakdown.set(key, {
                websiteUrl,
                widgetId,
                userCount: 0,
                countries: new Set(),
                cities: new Set()
            });
        }

        const breakdown = websiteWidgetBreakdown.get(key);
        breakdown.userCount++;
        breakdown.countries.add(userData.country || 'Unknown');
        breakdown.cities.add(userData.city || 'Unknown');
    });

    const websiteWidgetStats = Array.from(websiteWidgetBreakdown.values()).map(breakdown => ({
        websiteUrl: breakdown.websiteUrl,
        widgetId: breakdown.widgetId,
        userCount: breakdown.userCount,
        countries: Array.from(breakdown.countries),
        cities: Array.from(breakdown.cities)
    }));

    return {
        // Real-time metrics (for online users - unchanged)
        totalUsers,
        totalSessions,
        activeUsers: activeCount,
        idleUsers: idleCount,
        topPages, // ✅ FIXED: Now shows live users on pages
        usersByCountry: getRealTimeUsersByCountry(),
        usersByCity: getRealTimeUsersByCity(),

        // ✅ CRITICAL FIX: Breakdown by website and widget
        websiteWidgetBreakdown: websiteWidgetStats,
        
        // ✅ FIXED: Today's stats with capped time values
        todayStats: {
            unique_users: todayUniqueUsers,
            total_events: todayTotalEvents,
            unique_sessions: todayUniqueSessions,
            avg_time_on_page: Math.round(avgTimeOnPage), // ✅ FIXED: Rounded and capped
            avg_scroll_depth: Math.round(avgScrollDepth)
        },
        
        // ✅ FIXED: Top events (from today only)
        topEvents: interactions,
        
        // ✅ FIXED: Page performance with proper time calculations
        pageStats: pageStats,
        
        // ✅ FIXED: Behavior analytics with capped session durations
        behaviorStats: {
            sessions: todaySessions,
            interactions: interactions
        }
    };
}


// ✅ ENHANCED: Store analytics events to database
async function storeAnalyticsEvent(data) {
    let connection;
    try {
        connection = await pool.getConnection();
        
        await connection.execute(`
            INSERT INTO analytics_events (
                user_id, session_id, event_type, timestamp, url, path, title,
                session_duration, time_on_page, scroll_depth, click_count, keystrokes,
                device_info, browser_info, location_data, event_data,
                referrer, is_active, time_since_last_activity,
                website_url, widget_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            data.userId || null,
            data.sessionId || null,
            data.eventType || null,
            data.timestamp || Date.now(),
            data.url || null,
            data.path || null,
            data.title || null,
            data.sessionDuration || 0,
            data.timeOnPage || 0,
            data.scrollDepth || 0,
            data.clickCount || 0,
            data.keystrokes || 0,
            JSON.stringify(data.device || {}),
            JSON.stringify(data.browser || {}),
            JSON.stringify(data.location || {}),
            JSON.stringify(data.eventData || {}),
            data.referrer || null,
            data.isActive || false,
            data.timeSinceLastActivity || 0,
            data.websiteUrl || null,
            data.widgetId || null
        ]);
        
    } catch (error) {
        console.error('❌ [ANALYTICS] Database storage error:', error);
    } finally {
        if (connection) connection.release();
    }
}

async function processLocationData(userId, data) {
    const locationData = data.location;
    if (!locationData || !locationData.latitude || !locationData.longitude) {
        console.log(`❌ [ANALYTICS] No valid location data for ${userId}`);
        return;
    }

    await processLocationWithRetry(userId, {
        locationData: locationData,
        timestamp: data.timestamp,
        userAgent: data.browser?.userAgent,
        url: data.url,
        websiteUrl: data.websiteUrl,
        widgetId: data.widgetId
    });
}

async function handleLegacyUserOnline(ws, data) {
    const userId = data.userId;
    
    const userData = {
        ws: ws,
        userId: userId,
        lastSeen: Date.now(),
        country: 'Unknown',
        city: 'Unknown',
        currentUrl: data.url,
        isActive: true
    };
    
    activeUsers.set(userId, userData);
    console.log(`👤 [LEGACY] User ${userId} connected. Total users: ${activeUsers.size}`);
    
    if (data.locationData?.latitude && data.locationData?.longitude) {
        await processLocationWithRetry(userId, data);
    }
    
    broadcastUserCount();
    broadcastDashboardData();
}

// Keep all your existing location processing functions unchanged
async function processLocationWithRetry(userId, data, retryCount = 0) {
    try {
        const { timestamp, userAgent, url, locationData, permissionState, locationStatus, errorCode, errorMessage, websiteUrl, widgetId } = data;
        
        console.log(`🔍 [DEBUG] Full incoming data for ${userId}:`, {
            hasLocationData: !!locationData,
            locationData: locationData,
            permissionState: permissionState,
            locationStatus: locationStatus,
            errorCode: errorCode,
            errorMessage: errorMessage,
            timestamp: timestamp,
            userAgent: userAgent?.substring(0, 50) + '...'
        });
        
        if (!locationData) {
            console.log(`❌ [DEBUG] ${userId}: No location data received`);
            const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
            const userData = activeUsers.get(userCompositeKey);
            if (userData) {
                userData.country = 'Unknown';
                userData.city = 'Unknown';
                userData.locationStatus = locationStatus || 'no_location_data';
                activeUsers.set(userCompositeKey, userData);
            }
            broadcastDashboardData();
            return;
        }
        
        const { latitude, longitude, accuracy, altitude, speed } = locationData;
        
        console.log(`🔍 [DEBUG] ${userId} coordinates:`, {
            latitude: latitude,
            longitude: longitude,
            accuracy: accuracy,
            altitude: altitude,
            speed: speed,
            latType: typeof latitude,
            lonType: typeof longitude
        });
        
        if (!latitude || !longitude || typeof latitude !== 'number' || typeof longitude !== 'number') {
            console.log(`❌ [DEBUG] ${userId}: Invalid coordinates - lat: ${latitude}, lon: ${longitude}`);
            const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
            const userData = activeUsers.get(userCompositeKey);
            if (userData) {
                userData.country = 'Unknown';
                userData.city = 'Unknown';
                userData.locationStatus = 'invalid_coordinates';
                activeUsers.set(userCompositeKey, userData);
            }
            broadcastDashboardData();
            return;
        }
        
        if (latitude === 0 && longitude === 0) {
            console.log(`❌ [DEBUG] ${userId}: Zero coordinates received`);
            const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
            const userData = activeUsers.get(userCompositeKey);
            if (userData) {
                userData.country = 'Unknown';
                userData.city = 'Unknown';
                userData.locationStatus = 'zero_coordinates';
                activeUsers.set(userCompositeKey, userData);
            }
            broadcastDashboardData();
            return;
        }
        
        console.log(`🌍 [DEBUG] Processing valid location for ${userId}: ${latitude}, ${longitude} (accuracy: ${accuracy}m)`);
        
        const roundedLat = Math.round(latitude * 100) / 100;
        const roundedLon = Math.round(longitude * 100) / 100;
        const cacheKey = `${roundedLat},${roundedLon}`;
        
        if (locationCache.has(cacheKey)) {
            console.log(`📍 [DEBUG] Using cached location for ${userId}:`, locationCache.get(cacheKey));
            const cachedData = locationCache.get(cacheKey);
            updateUserLocation(userId, cachedData, websiteUrl, widgetId);
            await storeToDatabaseAsync(userId, timestamp, latitude, longitude, cachedData, accuracy, altitude, speed, userAgent, url, websiteUrl, widgetId);
            broadcastDashboardData();
            return;
        }
        
        if (pendingRequests.has(cacheKey)) {
            console.log(`⏳ [DEBUG] Location request pending for ${userId}, waiting...`);
            try {
                const cachedData = await pendingRequests.get(cacheKey);
                updateUserLocation(userId, cachedData, websiteUrl, widgetId);
                await storeToDatabaseAsync(userId, timestamp, latitude, longitude, cachedData, accuracy, altitude, speed, userAgent, url, websiteUrl, widgetId);
                broadcastDashboardData();
                return;
            } catch (error) {
                console.warn(`⚠️ [DEBUG] Pending request failed for ${userId}:`, error);
            }
        }
        
        const now = Date.now();
        const timeSinceLastCall = now - lastApiCall;
        if (timeSinceLastCall < API_RATE_LIMIT) {
            const waitTime = API_RATE_LIMIT - timeSinceLastCall;
            console.log(`⏱️ [DEBUG] Rate limiting: waiting ${waitTime}ms for ${userId}`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        const locationPromise = makeLocationIQRequest(latitude, longitude);
        pendingRequests.set(cacheKey, locationPromise);
        
        try {
            const addressData = await locationPromise;
            console.log(`📍 [DEBUG] LocationIQ response for ${userId}:`, addressData);
            
            locationCache.set(cacheKey, addressData);
            updateUserLocation(userId, addressData, websiteUrl, widgetId);
            
            console.log(`✅ [DEBUG] Location resolved for ${userId}: ${addressData.city}, ${addressData.country}`);
            
            await storeToDatabaseAsync(userId, timestamp, latitude, longitude, addressData, accuracy, altitude, speed, userAgent, url, websiteUrl, widgetId);
            broadcastDashboardData();
            
        } catch (error) {
            console.error(`❌ [DEBUG] LocationIQ API failed for ${userId}:`, error);
            
            if (retryCount < 3) {
                console.log(`🔄 [DEBUG] Retrying location for ${userId} (attempt ${retryCount + 1})`);
                setTimeout(() => {
                    processLocationWithRetry(userId, data, retryCount + 1);
                }, (retryCount + 1) * 2000);
            } else {
                console.error(`❌ [DEBUG] Max retries reached for ${userId}, keeping as Unknown`);
                const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
                const userData = activeUsers.get(userCompositeKey);
                if (userData) {
                    userData.country = 'Unknown';
                    userData.city = 'Unknown';
                    userData.locationStatus = 'api_failed';
                    activeUsers.set(userCompositeKey, userData);
                }
                broadcastDashboardData();
            }
        } finally {
            pendingRequests.delete(cacheKey);
            lastApiCall = Date.now();
        }
        
    } catch (error) {
        console.error(`❌ [DEBUG] Location processing error for ${userId}:`, error);
    }
}

async function makeLocationIQRequest(latitude, longitude) {
    const apiUrl = 'https://us1.locationiq.com/v1/reverse.php';
    const params = new URLSearchParams({
        key: LOCATIONIQ_TOKEN,
        lat: latitude,
        lon: longitude,
        format: 'json',
        addressdetails: 1
    });

    console.log(`🔍 [DEBUG] LocationIQ request: ${apiUrl}?${params}`);

    const response = await fetch(`${apiUrl}?${params}`);
    
    if (!response.ok) {
        console.error(`❌ [DEBUG] LocationIQ API error: ${response.status} ${response.statusText}`);
        throw new Error(`LocationIQ API error: ${response.status} ${response.statusText}`);
    }
    
    const locationResponse = await response.json();
    console.log(`🔍 [DEBUG] LocationIQ raw response:`, locationResponse);
    
    if (!locationResponse.address) {
        console.warn(`⚠️ [DEBUG] LocationIQ returned no address data:`, locationResponse);
        return {
            display_name: 'Unknown location',
            country: 'Unknown',
            city: 'Unknown',
            region: 'Unknown',
            state: 'Unknown',
            postcode: '',
            road: '',
            suburb: '',
            county: '',
            country_code: ''
        };
    }
    
    const result = {
        display_name: locationResponse.display_name || 'Unknown location',
        country: locationResponse.address?.country || 'Unknown',
        city: locationResponse.address?.city || 
              locationResponse.address?.town || 
              locationResponse.address?.village || 
              locationResponse.address?.municipality || 
              locationResponse.address?.hamlet || 'Unknown',
        region: locationResponse.address?.region || 
               locationResponse.address?.state || 
               locationResponse.address?.province || 'Unknown',
        state: locationResponse.address?.state || 
              locationResponse.address?.province || 
              locationResponse.address?.region || 'Unknown',
        postcode: locationResponse.address?.postcode || '',
        road: locationResponse.address?.road || 
             locationResponse.address?.street || '',
        suburb: locationResponse.address?.suburb || 
               locationResponse.address?.neighbourhood || 
               locationResponse.address?.quarter || '',
        county: locationResponse.address?.county || '',
        country_code: locationResponse.address?.country_code || ''
    };
    
    console.log(`🔍 [DEBUG] LocationIQ processed result:`, result);
    return result;
}

function updateUserLocation(userId, addressData, websiteUrl, widgetId) {
    // ✅ CRITICAL FIX: Use composite key to find and update user location
    const userCompositeKey = createCompositeKey(userId, websiteUrl, widgetId);
    const user = activeUsers.get(userCompositeKey);
    if (user) {
        user.country = addressData.country;
        user.city = addressData.city;
        activeUsers.set(userCompositeKey, user);
        console.log(`🌍 Updated ${userId} location: ${addressData.city}, ${addressData.country} (${websiteUrl}|${widgetId})`);

        console.log(`🔍 Current active users after location update:`, Array.from(activeUsers.entries()).map(([compositeKey, data]) => ({
            compositeKey,
            userId: data.userId,
            country: data.country,
            city: data.city,
            websiteUrl: data.websiteUrl,
            widgetId: data.widgetId
        })));
    } else {
        console.log(`❌ User not found for location update: ${userCompositeKey}`);
    }
}

async function storeToDatabaseAsync(userId, timestamp, latitude, longitude, addressData, accuracy, altitude, speed, userAgent, url, websiteUrl, widgetId) {
    let connection;
    try {
        connection = await pool.getConnection();
        await connection.execute(`
            INSERT INTO location_stats (
                user_id, timestamp, latitude, longitude, city, country, region,
                state, postcode, road, suburb, county, display_name, country_code, accuracy,
                altitude, speed, user_agent, url, website_url, widget_id
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            userId, timestamp, latitude, longitude,
            addressData.city, addressData.country, addressData.region,
            addressData.state, addressData.postcode, addressData.road,
            addressData.suburb, addressData.county, addressData.display_name,
            addressData.country_code, accuracy, altitude, speed,
            userAgent, url, websiteUrl, widgetId
        ]);
        
        console.log(`✅ Database updated for ${userId}`);
        
    } catch (error) {
        console.error('❌ Database storage error:', error);
    } finally {
        if (connection) connection.release();
    }
}

function broadcastUserCount() {
    const count = activeUsers.size;
    const message = JSON.stringify({
        type: 'live_users_count',
        count: count
    });
    
    activeUsers.forEach((user) => {
        if (user.ws.readyState === WebSocket.OPEN) {
            user.ws.send(message);
        }
    });
    
    console.log(`📊 ${count} users online`);
}

function getRealTimeUsersByCountry() {
    try {
        const countryCount = new Map();
        
        console.log(`🔍 getRealTimeUsersByCountry - Active users count: ${activeUsers.size}`);
        
        activeUsers.forEach((userData, userId) => {
            const country = userData.country || 'Unknown';
            countryCount.set(country, (countryCount.get(country) || 0) + 1);
            console.log(`🔍 User ${userId}: country = ${country}`);
        });
        
        const result = Array.from(countryCount.entries()).map(([country, count]) => ({
            country: country,
            user_count: count
        })).sort((a, b) => b.user_count - a.user_count);
        
        console.log(`🔍 getRealTimeUsersByCountry result:`, result);
        return result;
    } catch (error) {
        console.error('❌ Error in getRealTimeUsersByCountry:', error);
        return [];
    }
}

function getRealTimeUsersByCity() {
    try {
        const cityCount = new Map();
        
        console.log(`🔍 getRealTimeUsersByCity - Active users count: ${activeUsers.size}`);
        
        activeUsers.forEach((userData, userId) => {
            const city = userData.city || 'Unknown';
            const country = userData.country || 'Unknown';
            const cityKey = `${city}, ${country}`;
            cityCount.set(cityKey, (cityCount.get(cityKey) || 0) + 1);
            console.log(`🔍 User ${userId}: city = ${city}, country = ${country}, cityKey = ${cityKey}`);
        });
        
        const result = Array.from(cityCount.entries()).map(([city, count]) => ({
            city: city,
            user_count: count
        })).sort((a, b) => b.user_count - a.user_count);
        
        console.log(`🔍 getRealTimeUsersByCity result:`, result);
        return result;
    } catch (error) {
        console.error('❌ Error in getRealTimeUsersByCity:', error);
        return [];
    }
}

async function sendDashboardData(ws) {
    try {
        console.log('📊 Sending FIXED comprehensive dashboard data...');
        
        const realTimeAnalytics = getRealTimeAnalytics();
        
        const dashboardData = {
            type: 'dashboard_data',
            ...realTimeAnalytics,
            timestamp: Date.now(),
            dataType: 'real-time-analytics'
        };
        
        console.log('📊 [DEBUG] FIXED dashboard data being sent:', {
            totalUsers: dashboardData.totalUsers,
            totalSessions: dashboardData.totalSessions,
            todayStats: dashboardData.todayStats,
            pageStatsCount: dashboardData.pageStats?.length || 0,
            behaviorStatsSessionsCount: dashboardData.behaviorStats?.sessions?.length || 0,
            topPagesCount: dashboardData.topPages?.length || 0,
            topEventsCount: dashboardData.topEvents?.length || 0
        });
        
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(dashboardData));
            console.log('✅ FIXED comprehensive dashboard data sent successfully');
        }
    } catch (error) {
        console.error('❌ Error sending dashboard data:', error);
    }
}

async function broadcastDashboardData() {
    if (dashboardClients.size === 0) {
        console.log('📊 No dashboard clients to broadcast to');
        return;
    }
    
    try {
        console.log(`📊 Broadcasting FIXED dashboard data to ${dashboardClients.size} clients...`);
        
        const realTimeAnalytics = getRealTimeAnalytics();
        
        const dashboardData = {
            type: 'dashboard_data',
            ...realTimeAnalytics,
            timestamp: Date.now(),
            dataType: 'real-time-analytics'
        };
        
        const message = JSON.stringify(dashboardData);
        
        dashboardClients.forEach((client) => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            } else {
                dashboardClients.delete(client);
            }
        });
        
        console.log(`✅ FIXED comprehensive analytics data broadcasted to ${dashboardClients.size} clients`);
    } catch (error) {
        console.error('❌ Error broadcasting dashboard data:', error);
    }
}

// Keep existing API endpoints unchanged
app.get('/api/analytics/overview', async (req, res) => {
    try {
        const realTimeData = getRealTimeAnalytics();
        
        const connection = await pool.getConnection();
        
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);
        const todayTimestamp = todayStart.getTime();
        
        const [todayStats] = await connection.execute(`
            SELECT 
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(*) as total_events,
                COUNT(DISTINCT session_id) as unique_sessions,
                AVG(time_on_page) as avg_time_on_page,
                AVG(scroll_depth) as avg_scroll_depth
            FROM analytics_events 
            WHERE timestamp >= ?
        `, [todayTimestamp]);
        
        const [topEvents] = await connection.execute(`
            SELECT 
                event_type,
                COUNT(*) as event_count
            FROM analytics_events 
            WHERE timestamp >= ?
            GROUP BY event_type
            ORDER BY event_count DESC
            LIMIT 10
        `, [todayTimestamp]);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                realTime: realTimeData,
                today: todayStats[0] || {},
                topEvents: topEvents,
                timestamp: Date.now()
            }
        });
        
    } catch (error) {
        console.error('❌ Analytics overview error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error' 
        });
    }
});

app.get('/api/analytics/pages', async (req, res) => {
    try {
        const { date, limit = 50 } = req.query;
        const connection = await pool.getConnection();
        
        let whereClause = '';
        const params = [parseInt(limit)];
        
        if (date) {
            const dayStart = new Date(date).getTime();
            const dayEnd = dayStart + (24 * 60 * 60 * 1000);
            whereClause = 'WHERE timestamp >= ? AND timestamp < ?';
            params.unshift(dayStart, dayEnd);
        }
        
        const [pageStats] = await connection.execute(`
            SELECT 
                path,
                title,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(*) as page_views,
                AVG(time_on_page) as avg_time_on_page,
                AVG(scroll_depth) as avg_scroll_depth,
                COUNT(DISTINCT session_id) as unique_sessions
            FROM analytics_events 
            WHERE event_type = 'page_view' ${whereClause.replace('WHERE', 'AND')}
            GROUP BY path, title
            ORDER BY unique_users DESC
            LIMIT ?
        `, params);
        
        connection.release();
        
        res.json({
            success: true,
            data: pageStats,
            date: date || 'all-time'
        });
        
    } catch (error) {
        console.error('❌ Page analytics error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error' 
        });
    }
});

app.get('/api/analytics/behavior', async (req, res) => {
    try {
        const { date } = req.query;
        const connection = await pool.getConnection();
        
        let whereClause = '';
        const params = [];
        
        if (date) {
            const dayStart = new Date(date).getTime();
            const dayEnd = dayStart + (24 * 60 * 60 * 1000);
            whereClause = 'WHERE timestamp >= ? AND timestamp < ?';
            params.push(dayStart, dayEnd);
        }
        
        const [sessionStats] = await connection.execute(`
            SELECT 
                session_id,
                user_id,
                MIN(timestamp) as session_start,
                MAX(timestamp) as session_end,
                (MAX(timestamp) - MIN(timestamp)) as session_duration,
                COUNT(*) as events_count,
                COUNT(DISTINCT path) as pages_visited
            FROM analytics_events 
            ${whereClause}
            GROUP BY session_id, user_id
            ORDER BY session_duration DESC
        `, params);
        
        const [interactionStats] = await connection.execute(`
            SELECT 
                event_type,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as unique_users
            FROM analytics_events 
            ${whereClause}
            GROUP BY event_type
            ORDER BY count DESC
        `, params);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                sessions: sessionStats,
                interactions: interactionStats
            },
            date: date || 'all-time'
        });
        
    } catch (error) {
        console.error('❌ Behavior analytics error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error' 
        });
    }
});

// Your existing API endpoints
app.get('/api/available-dates', async (req, res) => {
    try {
        console.log('📊 Fetching all available dates (unlimited)...');
        
        const connection = await pool.getConnection();
        
        // ✅ Get raw timestamps and handle timezone in JavaScript
        const [rows] = await connection.execute(`
            SELECT 
                timestamp,
                COUNT(DISTINCT user_id) as user_count
            FROM location_stats 
            WHERE timestamp IS NOT NULL
            GROUP BY DATE(FROM_UNIXTIME(timestamp/1000))
            ORDER BY timestamp DESC
        `);
        
        connection.release();
        
        // ✅ Process dates in JavaScript to avoid SQL timezone issues
        const dateGroups = {};
        
        rows.forEach(row => {
            // Create date in UTC to avoid timezone shifts
            const date = new Date(row.timestamp);
            const dateKey = date.getUTCFullYear() + '-' + 
                           String(date.getUTCMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getUTCDate()).padStart(2, '0');
            
            if (!dateGroups[dateKey]) {
                dateGroups[dateKey] = row.user_count;
            }
        });
        
        // Convert to array format
        const cleanDates = Object.entries(dateGroups)
            .map(([date, user_count]) => ({ date, user_count }))
            .sort((a, b) => b.date.localeCompare(a.date));
        
        console.log(`📊 Found ${cleanDates.length} dates with CLEAN data:`, cleanDates);
        
        res.json({
            success: true,
            dates: cleanDates,
            totalDates: cleanDates.length
        });
    } catch (error) {
        console.error('❌ Available dates API error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error',
            details: error.message 
        });
    }
});

// Get historical data for a specific date
app.post('/api/historical-dashboard', async (req, res) => {
    try {
        const { date } = req.body; // Can be 'YYYY-MM-DD' or ISO string
        
        if (!date) {
            return res.status(400).json({ 
                success: false, 
                error: 'Date is required' 
            });
        }

        console.log(`📊 Fetching historical data for date: ${date}`);

        const historicalData = await getHistoricalData(date);
        
        res.json({
            success: true,
            data: historicalData,
            date: date,
            dataType: 'historical'
        });
    } catch (error) {
        console.error('❌ Historical dashboard API error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error',
            details: error.message 
        });
    }
});

// ✅ IMPROVED: Function to get historical data with better date handling
async function getHistoricalData(dateInput) {
    let connection;
    try {
        connection = await pool.getConnection();
        
        // ✅ FIXED: Better date parsing with timezone handling
        let dateOnly;
        if (dateInput.includes('T')) {
            // If it's an ISO string, extract just the date part
            dateOnly = dateInput.split('T')[0];
        } else {
            // If it's already in YYYY-MM-DD format, use as is
            dateOnly = dateInput;
        }
        
        // Validate date format (YYYY-MM-DD)
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(dateOnly)) {
            throw new Error(`Invalid date format: ${dateInput}. Expected YYYY-MM-DD`);
        }
        
        console.log(`📊 Processing date: ${dateOnly} from input: ${dateInput}`);
        
        // ✅ FIXED: Create timestamps for the specific date in UTC
        // This ensures we get the full 24-hour period for the specified date
        const startOfDay = new Date(`${dateOnly}T00:00:00.000Z`);
        const endOfDay = new Date(`${dateOnly}T23:59:59.999Z`);
        
        const startTimestamp = startOfDay.getTime();
        const endTimestamp = endOfDay.getTime();
        
        console.log(`📊 Date range: ${startOfDay.toISOString()} to ${endOfDay.toISOString()}`);
        console.log(`📊 Timestamp range: ${startTimestamp} - ${endTimestamp}`);
        
        // Verify timestamps are valid
        if (isNaN(startTimestamp) || isNaN(endTimestamp)) {
            throw new Error(`Invalid timestamps generated from date: ${dateOnly}`);
        }
        
        // ✅ ADDED: Debug query to show what data exists in the date range
        const [debugResult] = await connection.execute(`
            SELECT 
                user_id,
                timestamp,
                FROM_UNIXTIME(timestamp/1000) as readable_time,
                city,
                country
            FROM location_stats 
            WHERE timestamp >= ? AND timestamp <= ?
            ORDER BY timestamp DESC
            LIMIT 5
        `, [startTimestamp, endTimestamp]);
        
        console.log(`📊 Sample data in range:`, debugResult);
        
        // Get total unique users for the date
        const [totalResult] = await connection.execute(`
            SELECT COUNT(DISTINCT user_id) as total_users
            FROM location_stats 
            WHERE timestamp >= ? AND timestamp <= ?
        `, [startTimestamp, endTimestamp]);
        
        console.log(`📊 Total users found: ${totalResult[0].total_users}`);
        
        // Get users by country for the date
        const [countryResult] = await connection.execute(`
            SELECT 
                country,
                COUNT(DISTINCT user_id) as user_count
            FROM location_stats 
            WHERE timestamp >= ? AND timestamp <= ?
            AND country IS NOT NULL 
            AND country != ''
            AND country != 'Unknown'
            GROUP BY country
            ORDER BY user_count DESC
        `, [startTimestamp, endTimestamp]);
        
        // Get users by city for the date
        const [cityResult] = await connection.execute(`
            SELECT 
                CONCAT(city, ', ', country) as city,
                COUNT(DISTINCT user_id) as user_count
            FROM location_stats 
            WHERE timestamp >= ? AND timestamp <= ?
            AND city IS NOT NULL 
            AND city != ''
            AND city != 'Unknown'
            AND country IS NOT NULL 
            AND country != ''
            AND country != 'Unknown'
            GROUP BY city, country
            ORDER BY user_count DESC
        `, [startTimestamp, endTimestamp]);
        
        const historicalData = {
            totalOnline: totalResult[0].total_users,
            usersByCountry: countryResult.map(row => ({
                country: row.country,
                user_count: row.user_count
            })),
            usersByCity: cityResult.map(row => ({
                city: row.city,
                user_count: row.user_count
            })),
            timestamp: Date.now(),
            dataType: 'historical',
            date: dateOnly,
            // ✅ ADDED: Include debug info
            queryRange: {
                start: startOfDay.toISOString(),
                end: endOfDay.toISOString(),
                startTimestamp,
                endTimestamp
            }
        };
        
        console.log(`📊 Final historical data for ${dateOnly}:`, {
            totalUsers: historicalData.totalOnline,
            countries: historicalData.usersByCountry.length,
            cities: historicalData.usersByCity.length,
            dateRequested: dateOnly,
            queryRange: historicalData.queryRange
        });
        
        return historicalData;
        
    } catch (error) {
        console.error('❌ Error fetching historical data:', error);
        throw error;
    } finally {
        if (connection) connection.release();
    }
}

// Existing endpoints
app.get('/api/dashboard', async (req, res) => {
    try {
        const realTimeAnalytics = getRealTimeAnalytics();
        
        res.json({
            ...realTimeAnalytics,
            timestamp: Date.now(),
            dataType: 'real-time-analytics'
        });
    } catch (error) {
        console.error('❌ Dashboard API error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy',
        online_users: activeUsers.size,
        active_sessions: activeSessions.size,
        dashboard_clients: dashboardClients.size,
        location_cache_size: locationCache.size,
        page_views: pageViews.size
    });
});

// ✅ NEW: Auto-cleanup inactive sessions (every 5 minutes)
setInterval(() => {
    const now = Date.now();
    const INACTIVE_THRESHOLD = 10 * 60 * 1000; // 10 minutes
    
    let cleaned = 0;
    
    // Clean inactive users
    activeUsers.forEach((user, userId) => {
        if (now - user.lastSeen > INACTIVE_THRESHOLD) {
            activeUsers.delete(userId);
            cleaned++;
        }
    });
    
    // Clean inactive sessions
    activeSessions.forEach((session, sessionId) => {
        if (now - session.lastActivity > INACTIVE_THRESHOLD) {
            activeSessions.delete(sessionId);
        }
    });
    
    // Clean page views
    pageViews.forEach((users, page) => {
        users.forEach(userId => {
            if (!activeUsers.has(userId)) {
                users.delete(userId);
            }
        });
        if (users.size === 0) {
            pageViews.delete(page);
        }
    });
    
    if (cleaned > 0) {
        console.log(`🗑️ Cleaned ${cleaned} inactive users`);
        broadcastUserCount();
        broadcastDashboardData();
    }
}, 5 * 60 * 1000);

// Cache cleanup (run every 30 minutes)
setInterval(() => {
    locationCache.clear();
    console.log('🗑️ Location cache cleared');
}, 30 * 60 * 1000);

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`🚀 Enhanced Analytics Server running on port ${PORT}`);
    console.log(`🔌 WebSocket: ws://localhost:8080`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
    console.log(`📊 Analytics Overview: http://localhost:${PORT}/api/analytics/overview`);
    console.log(`📊 Page Analytics: http://localhost:${PORT}/api/analytics/pages`);
    console.log(`📊 Behavior Analytics: http://localhost:${PORT}/api/analytics/behavior`);
    console.log(`📊 Historical API: http://localhost:${PORT}/api/historical-dashboard`);
    console.log(`📊 Available Dates API: http://localhost:${PORT}/api/available-dates`);
});
