{"ast": null, "code": "var _jsxFileName = \"D:\\\\DataGen\\\\authkey-chat-new\\\\src\\\\components\\\\ReportSections\\\\OnlineUsersCount.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useState, useEffect } from 'react';\nimport { ChatState } from '../../context/AllProviders';\nimport { getCountryDataWithFlags } from '../../utils/countryFlags';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst OnlineUsersCount = () => {\n  _s();\n\n  const {\n    onlineUsersData,\n    historicalMode,\n    selectedDate,\n    historicalData,\n    availableDates,\n    fetchAvailableDates,\n    switchToHistoricalMode,\n    switchToRealTimeMode,\n    loading\n  } = ChatState();\n  const [activeTab, setActiveTab] = useState('country');\n  const [showDatePicker, setShowDatePicker] = useState(false);\n  const [selectedWebsite, setSelectedWebsite] = useState('all');\n  const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false); // Fetch available dates on component mount\n\n  useEffect(() => {\n    fetchAvailableDates();\n  }, []); // Close dropdowns when clicking outside\n\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showDatePicker && !event.target.closest('.date-picker-container')) {\n        setShowDatePicker(false);\n      }\n\n      if (showWebsiteDropdown && !event.target.closest('.website-dropdown-container')) {\n        setShowWebsiteDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showDatePicker, showWebsiteDropdown]); // Determine which data to use based on mode\n\n  const currentData = historicalMode ? historicalData : onlineUsersData; // ✅ CRITICAL FIX: Extract website breakdown data and create website options\n\n  const websiteBreakdown = currentData.websiteWidgetBreakdown || [];\n  const websiteOptions = [{\n    value: 'all',\n    label: 'All Websites',\n    count: currentData.totalOnline || 0\n  }, ...websiteBreakdown.map(item => ({\n    value: `${item.websiteUrl}|${item.widgetId}`,\n    label: item.websiteUrl || 'Unknown Website',\n    widgetId: item.widgetId,\n    count: item.userCount,\n    countries: item.countries || [],\n    cities: item.cities || []\n  }))]; // ✅ CRITICAL FIX: Filter data based on selected website\n\n  let filteredData = currentData;\n\n  if (selectedWebsite !== 'all') {\n    const selectedBreakdown = websiteBreakdown.find(item => `${item.websiteUrl}|${item.widgetId}` === selectedWebsite);\n\n    if (selectedBreakdown) {\n      // Create filtered data structure for selected website\n      filteredData = { ...currentData,\n        totalOnline: selectedBreakdown.userCount,\n        usersByCountry: selectedBreakdown.countries.map(country => ({\n          country: country,\n          user_count: 1 // This would need to be calculated properly from server\n\n        })),\n        usersByCity: selectedBreakdown.cities.map(city => ({\n          city: city,\n          user_count: 1 // This would need to be calculated properly from server\n\n        }))\n      };\n    }\n  }\n\n  const countryWiseData = getCountryDataWithFlags(filteredData.usersByCountry || []);\n  const cityWiseData = filteredData.usersByCity || [];\n  const totalLiveUsers = filteredData.totalOnline || 0;\n  const isConnected = historicalMode ? true : onlineUsersData.isConnected; // Helper functions\n\n  const parseCityData = cityString => {\n    if (!cityString || cityString === 'Unknown') {\n      return {\n        city: 'Unknown',\n        country: 'Unknown'\n      };\n    }\n\n    const parts = cityString.split(', ');\n\n    if (parts.length >= 2) {\n      const city = parts[0];\n      const country = parts.slice(1).join(', ');\n      return {\n        city,\n        country\n      };\n    }\n\n    return {\n      city: cityString,\n      country: 'Unknown'\n    };\n  };\n\n  const getCityAbbreviation = cityName => {\n    if (!cityName || cityName === 'Unknown') return 'UN';\n    const cleanedCity = cityName.replace(/\\b(City|Town|Village|Municipality)\\b/gi, '').trim();\n    const words = cleanedCity.split(' ').filter(word => word.length > 0);\n\n    if (words.length >= 2) {\n      return (words[0][0] + words[1][0]).toUpperCase();\n    } else if (words.length === 1) {\n      const word = words[0];\n\n      if (word.length >= 2) {\n        return word.substring(0, 2).toUpperCase();\n      } else {\n        return (word + 'X').toUpperCase();\n      }\n    }\n\n    return 'UN';\n  };\n\n  const filteredCityData = cityWiseData.filter(item => {\n    const {\n      city\n    } = parseCityData(item.city);\n    return city !== 'Unknown';\n  });\n\n  const handleDateSelect = dateInfo => {\n    console.log('🔍 Raw dateInfo received:', dateInfo); // ✅ FIXED: Extract clean date regardless of format\n\n    let cleanDate;\n\n    if (typeof dateInfo === 'string') {\n      cleanDate = dateInfo.includes('T') ? dateInfo.split('T')[0] : dateInfo;\n    } else if (dateInfo && dateInfo.date) {\n      cleanDate = dateInfo.date.includes('T') ? dateInfo.date.split('T')[0] : dateInfo.date;\n    } else {\n      console.error('❌ Invalid date format received:', dateInfo);\n      return;\n    }\n\n    console.log(`📅 User selected clean date: ${cleanDate}`);\n    switchToHistoricalMode(cleanDate);\n    setShowDatePicker(false);\n  };\n\n  const formatDate = dateString => {\n    console.log('🔍 formatDate input:', dateString);\n    const date = new Date(dateString);\n    console.log('🔍 formatDate parsed date:', date);\n    console.log('🔍 formatDate timezone offset:', date.getTimezoneOffset());\n    const formatted = date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n    console.log('🔍 formatDate output:', formatted);\n    return formatted;\n  };\n\n  const formatDateShort = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white shadow-sm\",\n    style: {\n      borderRadius: \"12px\",\n      width: \"100%\",\n      maxWidth: \"320px\",\n      flexShrink: 0,\n      border: \"1px solid #e9ecef\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 border-bottom\",\n      style: {\n        backgroundColor: \"#f8f9fa\",\n        borderRadius: \"12px 12px 0 0\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0 fw-semibold\",\n          style: {\n            color: \"#495057\"\n          },\n          children: historicalMode ? 'Historical Users' : 'Live Users'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [!historicalMode && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-circle me-2 ${isConnected ? 'bg-success' : 'bg-danger'}`,\n              style: {\n                width: \"8px\",\n                height: \"8px\"\n              },\n              title: isConnected ? 'Connected' : 'Disconnected'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              style: {\n                fontSize: \"12px\"\n              },\n              children: isConnected ? 'Live' : 'Offline'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true), historicalMode && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-muted\",\n            style: {\n              fontSize: \"12px\"\n            },\n            children: formatDateShort(selectedDate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        style: {\n          gap: \"8px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `btn btn-sm ${!historicalMode ? 'btn-success' : 'btn-outline-success'}`,\n          style: {\n            borderRadius: \"6px\",\n            fontSize: \"11px\",\n            fontWeight: \"600\",\n            flex: 1\n          },\n          onClick: switchToRealTimeMode,\n          disabled: loading,\n          children: \"Live\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"position-relative date-picker-container\",\n          style: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn btn-sm w-100 ${historicalMode ? 'btn-primary' : 'btn-outline-primary'}`,\n            style: {\n              borderRadius: \"6px\",\n              fontSize: \"11px\",\n              fontWeight: \"600\"\n            },\n            onClick: () => setShowDatePicker(!showDatePicker),\n            disabled: loading,\n            children: loading ? 'Loading...' : 'History ▼'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 25\n          }, this), showDatePicker && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"position-absolute\",\n            style: {\n              top: \"100%\",\n              left: 0,\n              right: 0,\n              zIndex: 1000,\n              backgroundColor: \"white\",\n              border: \"1px solid #dee2e6\",\n              borderRadius: \"8px\",\n              boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n              maxHeight: \"250px\",\n              overflowY: \"auto\",\n              marginTop: \"4px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-3 py-2 border-bottom\",\n              style: {\n                backgroundColor: \"#f8f9fa\",\n                fontSize: \"11px\",\n                fontWeight: \"600\",\n                color: \"#6c757d\"\n              },\n              children: [\"Select Date (\", availableDates.length, \" days available)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 33\n            }, this), availableDates.length > 0 ? availableDates.map((dateInfo, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-3 py-2\",\n              style: {\n                cursor: \"pointer\",\n                fontSize: \"12px\",\n                borderBottom: index < availableDates.length - 1 ? \"1px solid #f1f3f4\" : \"none\",\n                backgroundColor: selectedDate === dateInfo.date ? \"#e3f2fd\" : \"transparent\"\n              },\n              onMouseEnter: e => {\n                if (selectedDate !== dateInfo.date) {\n                  e.target.style.backgroundColor = \"#f8f9fa\";\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedDate !== dateInfo.date) {\n                  e.target.style.backgroundColor = \"transparent\";\n                }\n              },\n              onClick: () => handleDateSelect(dateInfo.date),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-semibold\",\n                    children: formatDate(dateInfo.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted\",\n                    style: {\n                      fontSize: \"10px\"\n                    },\n                    children: [dateInfo.user_count, \" unique users\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 49\n                }, this), selectedDate === dateInfo.date && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: \"#1976d2\",\n                    fontSize: \"10px\"\n                  },\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 45\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 41\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-3 py-4 text-muted text-center\",\n              style: {\n                fontSize: \"12px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\uD83D\\uDCC5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: \"No historical data available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center align-items-center mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green\",\n            style: {\n              background: historicalMode ? \"linear-gradient(135deg, #28a745, #20c997)\" : \"linear-gradient(135deg, #28a745, #20c997)\",\n              borderRadius: \"50%\",\n              width: \"60px\",\n              height: \"60px\",\n              display: \"flex\",\n              justifyContent: \"center\",\n              alignItems: \"center\",\n              boxShadow: historicalMode ? \"0 4px 8px rgba(111, 66, 193, 0.3)\" : \"0 4px 8px rgba(40, 167, 69, 0.3)\",\n              transition: \"all 0.3s ease\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white fw-bold fs-4\",\n              children: loading ? '...' : totalLiveUsers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-0\",\n          style: {\n            fontSize: \"13px\",\n            fontWeight: \"500\"\n          },\n          children: historicalMode ? selectedDate ? `Users on ${formatDate(selectedDate)}` : 'Historical Users' : 'Total Active Users'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        style: {\n          backgroundColor: \"#f8f9fa\",\n          borderRadius: \"8px\",\n          padding: \"4px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `flex-1 btn btn-sm ${activeTab === 'country' ? 'btn-primary' : 'btn-light'}`,\n          style: {\n            borderRadius: \"6px\",\n            fontSize: \"12px\",\n            fontWeight: \"600\",\n            border: \"none\",\n            transition: \"all 0.2s\"\n          },\n          onClick: () => setActiveTab('country'),\n          disabled: loading,\n          children: \"Countries\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `flex-1 btn btn-sm ${activeTab === 'city' ? 'btn-primary' : 'btn-light'}`,\n          style: {\n            borderRadius: \"6px\",\n            fontSize: \"12px\",\n            fontWeight: \"600\",\n            border: \"none\",\n            marginLeft: \"4px\",\n            transition: \"all 0.2s\"\n          },\n          onClick: () => setActiveTab('city'),\n          disabled: loading,\n          children: \"Cities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: \"200px\",\n          overflowY: \"auto\"\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border spinner-border-sm text-primary\",\n            role: \"status\",\n            style: {\n              fontSize: \"12px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-muted mt-2\",\n            style: {\n              fontSize: \"12px\"\n            },\n            children: [\"Loading \", historicalMode ? 'historical' : 'live', \" data...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: activeTab === 'country' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: countryWiseData.length > 0 ? countryWiseData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mb-2 p-2\",\n              style: {\n                backgroundColor: \"#fff\",\n                borderRadius: \"8px\",\n                border: \"1px solid #e9ecef\",\n                transition: \"all 0.2s\",\n                cursor: \"pointer\"\n              },\n              onMouseEnter: e => e.currentTarget.style.backgroundColor = \"#f8f9fa\",\n              onMouseLeave: e => e.currentTarget.style.backgroundColor = \"#fff\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"me-2\",\n                  style: {\n                    fontSize: \"18px\"\n                  },\n                  children: item.flag\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: \"13px\",\n                      fontWeight: \"600\",\n                      color: \"#495057\"\n                    },\n                    children: item.country\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: \"11px\",\n                      color: \"#6c757d\"\n                    },\n                    children: [totalLiveUsers > 0 ? (item.user_count / totalLiveUsers * 100).toFixed(1) : 0, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 49\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-primary me-2\",\n                  style: {\n                    fontSize: \"11px\"\n                  },\n                  children: item.user_count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress\",\n                  style: {\n                    width: \"40px\",\n                    height: \"4px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar bg-primary\",\n                    style: {\n                      width: totalLiveUsers > 0 ? `${item.user_count / totalLiveUsers * 100}%` : '0%',\n                      borderRadius: \"2px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 57\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 49\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 45\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted py-3\",\n              style: {\n                fontSize: \"13px\"\n              },\n              children: historicalMode ? selectedDate ? `No country data for ${formatDate(selectedDate)}` : 'No historical country data' : isConnected ? 'No country data available' : 'Connecting...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: filteredCityData.length > 0 ? filteredCityData.map((item, index) => {\n              const {\n                city,\n                country\n              } = parseCityData(item.city);\n              const cityAbbr = getCityAbbreviation(city);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center mb-2 p-2\",\n                style: {\n                  backgroundColor: \"#fff\",\n                  borderRadius: \"8px\",\n                  border: \"1px solid #e9ecef\",\n                  transition: \"all 0.2s\",\n                  cursor: \"pointer\"\n                },\n                onMouseEnter: e => e.currentTarget.style.backgroundColor = \"#f8f9fa\",\n                onMouseLeave: e => e.currentTarget.style.backgroundColor = \"#fff\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"me-2\",\n                    style: {\n                      width: \"20px\",\n                      height: \"20px\",\n                      backgroundColor: \"#e9ecef\",\n                      borderRadius: \"50%\",\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"center\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: \"8px\",\n                        fontWeight: \"bold\",\n                        color: \"#6c757d\",\n                        letterSpacing: \"-0.5px\"\n                      },\n                      children: cityAbbr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"13px\",\n                        fontWeight: \"600\",\n                        color: \"#495057\"\n                      },\n                      children: city\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"11px\",\n                        color: \"#6c757d\"\n                      },\n                      children: [country, \" \\u2022 \", totalLiveUsers > 0 ? (item.user_count / totalLiveUsers * 100).toFixed(1) : 0, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-primary me-2\",\n                    style: {\n                      fontSize: \"11px\"\n                    },\n                    children: item.user_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress\",\n                    style: {\n                      width: \"40px\",\n                      height: \"4px\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress-bar bg-primary\",\n                      style: {\n                        width: totalLiveUsers > 0 ? `${item.user_count / totalLiveUsers * 100}%` : '0%',\n                        borderRadius: \"2px\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 53\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 49\n              }, this);\n            }) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted py-3\",\n              style: {\n                fontSize: \"13px\"\n              },\n              children: historicalMode ? selectedDate ? `No city data for ${formatDate(selectedDate)}` : 'No historical city data' : isConnected ? 'No city data available' : 'Connecting...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 33\n          }, this)\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 pt-2 border-top\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between text-muted\",\n          style: {\n            fontSize: \"11px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [countryWiseData.length, \" Countries\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [filteredCityData.length, \" Cities\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: historicalMode ? 'Historical' : 'Live Updates'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 9\n  }, this);\n};\n\n_s(OnlineUsersCount, \"dyigvlwgynIi04x4aBsNQ9GGPp8=\");\n\n_c = OnlineUsersCount;\nexport default OnlineUsersCount;\n\nvar _c;\n\n$RefreshReg$(_c, \"OnlineUsersCount\");", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/components/ReportSections/OnlineUsersCount.jsx"], "names": ["React", "useState", "useEffect", "ChatState", "getCountryDataWithFlags", "OnlineUsersCount", "onlineUsersData", "historicalMode", "selectedDate", "historicalData", "availableDates", "fetchAvailableDates", "switchToHistoricalMode", "switchToRealTimeMode", "loading", "activeTab", "setActiveTab", "showDatePicker", "setShowDatePicker", "selectedWebsite", "setSelectedWebsite", "showWebsiteDropdown", "setShowWebsiteDropdown", "handleClickOutside", "event", "target", "closest", "document", "addEventListener", "removeEventListener", "currentData", "websiteBreakdown", "websiteWidgetBreakdown", "websiteOptions", "value", "label", "count", "totalOnline", "map", "item", "websiteUrl", "widgetId", "userCount", "countries", "cities", "filteredData", "selectedBreakdown", "find", "usersByCountry", "country", "user_count", "usersByCity", "city", "countryWiseData", "cityWiseData", "totalLiveUsers", "isConnected", "parseCityData", "cityString", "parts", "split", "length", "slice", "join", "getCityAbbreviation", "cityName", "cleanedCity", "replace", "trim", "words", "filter", "word", "toUpperCase", "substring", "filteredCityData", "handleDateSelect", "dateInfo", "console", "log", "cleanDate", "includes", "date", "error", "formatDate", "dateString", "Date", "getTimezoneOffset", "formatted", "toLocaleDateString", "year", "month", "day", "formatDateShort", "borderRadius", "width", "max<PERSON><PERSON><PERSON>", "flexShrink", "border", "backgroundColor", "color", "height", "fontSize", "gap", "fontWeight", "flex", "top", "left", "right", "zIndex", "boxShadow", "maxHeight", "overflowY", "marginTop", "index", "cursor", "borderBottom", "e", "style", "background", "display", "justifyContent", "alignItems", "transition", "padding", "marginLeft", "currentTarget", "flag", "toFixed", "cityAbbr", "letterSpacing"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,QAA2C,OAA3C;AACA,SAASC,SAAT,QAA0B,4BAA1B;AACA,SAASC,uBAAT,QAAwC,0BAAxC;;;;AAEA,MAAMC,gBAAgB,GAAG,MAAM;AAAA;;AAC3B,QAAM;AACFC,IAAAA,eADE;AAEFC,IAAAA,cAFE;AAGFC,IAAAA,YAHE;AAIFC,IAAAA,cAJE;AAKFC,IAAAA,cALE;AAMFC,IAAAA,mBANE;AAOFC,IAAAA,sBAPE;AAQFC,IAAAA,oBARE;AASFC,IAAAA;AATE,MAUFX,SAAS,EAVb;AAYA,QAAM,CAACY,SAAD,EAAYC,YAAZ,IAA4Bf,QAAQ,CAAC,SAAD,CAA1C;AACA,QAAM,CAACgB,cAAD,EAAiBC,iBAAjB,IAAsCjB,QAAQ,CAAC,KAAD,CAApD;AACA,QAAM,CAACkB,eAAD,EAAkBC,kBAAlB,IAAwCnB,QAAQ,CAAC,KAAD,CAAtD;AACA,QAAM,CAACoB,mBAAD,EAAsBC,sBAAtB,IAAgDrB,QAAQ,CAAC,KAAD,CAA9D,CAhB2B,CAkB3B;;AACAC,EAAAA,SAAS,CAAC,MAAM;AACZS,IAAAA,mBAAmB;AACtB,GAFQ,EAEN,EAFM,CAAT,CAnB2B,CAuB3B;;AACAT,EAAAA,SAAS,CAAC,MAAM;AACZ,UAAMqB,kBAAkB,GAAIC,KAAD,IAAW;AAClC,UAAIP,cAAc,IAAI,CAACO,KAAK,CAACC,MAAN,CAAaC,OAAb,CAAqB,wBAArB,CAAvB,EAAuE;AACnER,QAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACH;;AACD,UAAIG,mBAAmB,IAAI,CAACG,KAAK,CAACC,MAAN,CAAaC,OAAb,CAAqB,6BAArB,CAA5B,EAAiF;AAC7EJ,QAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACH;AACJ,KAPD;;AASAK,IAAAA,QAAQ,CAACC,gBAAT,CAA0B,WAA1B,EAAuCL,kBAAvC;AACA,WAAO,MAAM;AACTI,MAAAA,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0CN,kBAA1C;AACH,KAFD;AAGH,GAdQ,EAcN,CAACN,cAAD,EAAiBI,mBAAjB,CAdM,CAAT,CAxB2B,CAwC3B;;AACA,QAAMS,WAAW,GAAGvB,cAAc,GAAGE,cAAH,GAAoBH,eAAtD,CAzC2B,CA2C3B;;AACA,QAAMyB,gBAAgB,GAAGD,WAAW,CAACE,sBAAZ,IAAsC,EAA/D;AACA,QAAMC,cAAc,GAAG,CACnB;AAAEC,IAAAA,KAAK,EAAE,KAAT;AAAgBC,IAAAA,KAAK,EAAE,cAAvB;AAAuCC,IAAAA,KAAK,EAAEN,WAAW,CAACO,WAAZ,IAA2B;AAAzE,GADmB,EAEnB,GAAGN,gBAAgB,CAACO,GAAjB,CAAqBC,IAAI,KAAK;AAC7BL,IAAAA,KAAK,EAAG,GAAEK,IAAI,CAACC,UAAW,IAAGD,IAAI,CAACE,QAAS,EADd;AAE7BN,IAAAA,KAAK,EAAEI,IAAI,CAACC,UAAL,IAAmB,iBAFG;AAG7BC,IAAAA,QAAQ,EAAEF,IAAI,CAACE,QAHc;AAI7BL,IAAAA,KAAK,EAAEG,IAAI,CAACG,SAJiB;AAK7BC,IAAAA,SAAS,EAAEJ,IAAI,CAACI,SAAL,IAAkB,EALA;AAM7BC,IAAAA,MAAM,EAAEL,IAAI,CAACK,MAAL,IAAe;AANM,GAAL,CAAzB,CAFgB,CAAvB,CA7C2B,CAyD3B;;AACA,MAAIC,YAAY,GAAGf,WAAnB;;AACA,MAAIX,eAAe,KAAK,KAAxB,EAA+B;AAC3B,UAAM2B,iBAAiB,GAAGf,gBAAgB,CAACgB,IAAjB,CAAsBR,IAAI,IAC/C,GAAEA,IAAI,CAACC,UAAW,IAAGD,IAAI,CAACE,QAAS,EAApC,KAA0CtB,eADpB,CAA1B;;AAGA,QAAI2B,iBAAJ,EAAuB;AACnB;AACAD,MAAAA,YAAY,GAAG,EACX,GAAGf,WADQ;AAEXO,QAAAA,WAAW,EAAES,iBAAiB,CAACJ,SAFpB;AAGXM,QAAAA,cAAc,EAAEF,iBAAiB,CAACH,SAAlB,CAA4BL,GAA5B,CAAgCW,OAAO,KAAK;AACxDA,UAAAA,OAAO,EAAEA,OAD+C;AAExDC,UAAAA,UAAU,EAAE,CAF4C,CAE1C;;AAF0C,SAAL,CAAvC,CAHL;AAOXC,QAAAA,WAAW,EAAEL,iBAAiB,CAACF,MAAlB,CAAyBN,GAAzB,CAA6Bc,IAAI,KAAK;AAC/CA,UAAAA,IAAI,EAAEA,IADyC;AAE/CF,UAAAA,UAAU,EAAE,CAFmC,CAEjC;;AAFiC,SAAL,CAAjC;AAPF,OAAf;AAYH;AACJ;;AAED,QAAMG,eAAe,GAAGjD,uBAAuB,CAACyC,YAAY,CAACG,cAAb,IAA+B,EAAhC,CAA/C;AACA,QAAMM,YAAY,GAAGT,YAAY,CAACM,WAAb,IAA4B,EAAjD;AACA,QAAMI,cAAc,GAAGV,YAAY,CAACR,WAAb,IAA4B,CAAnD;AACA,QAAMmB,WAAW,GAAGjD,cAAc,GAAG,IAAH,GAAUD,eAAe,CAACkD,WAA5D,CAnF2B,CAqF3B;;AACA,QAAMC,aAAa,GAAIC,UAAD,IAAgB;AAClC,QAAI,CAACA,UAAD,IAAeA,UAAU,KAAK,SAAlC,EAA6C;AACzC,aAAO;AAAEN,QAAAA,IAAI,EAAE,SAAR;AAAmBH,QAAAA,OAAO,EAAE;AAA5B,OAAP;AACH;;AAED,UAAMU,KAAK,GAAGD,UAAU,CAACE,KAAX,CAAiB,IAAjB,CAAd;;AACA,QAAID,KAAK,CAACE,MAAN,IAAgB,CAApB,EAAuB;AACnB,YAAMT,IAAI,GAAGO,KAAK,CAAC,CAAD,CAAlB;AACA,YAAMV,OAAO,GAAGU,KAAK,CAACG,KAAN,CAAY,CAAZ,EAAeC,IAAf,CAAoB,IAApB,CAAhB;AACA,aAAO;AAAEX,QAAAA,IAAF;AAAQH,QAAAA;AAAR,OAAP;AACH;;AAED,WAAO;AAAEG,MAAAA,IAAI,EAAEM,UAAR;AAAoBT,MAAAA,OAAO,EAAE;AAA7B,KAAP;AACH,GAbD;;AAeA,QAAMe,mBAAmB,GAAIC,QAAD,IAAc;AACtC,QAAI,CAACA,QAAD,IAAaA,QAAQ,KAAK,SAA9B,EAAyC,OAAO,IAAP;AAEzC,UAAMC,WAAW,GAAGD,QAAQ,CACvBE,OADe,CACP,wCADO,EACmC,EADnC,EAEfC,IAFe,EAApB;AAIA,UAAMC,KAAK,GAAGH,WAAW,CAACN,KAAZ,CAAkB,GAAlB,EAAuBU,MAAvB,CAA8BC,IAAI,IAAIA,IAAI,CAACV,MAAL,GAAc,CAApD,CAAd;;AAEA,QAAIQ,KAAK,CAACR,MAAN,IAAgB,CAApB,EAAuB;AACnB,aAAO,CAACQ,KAAK,CAAC,CAAD,CAAL,CAAS,CAAT,IAAcA,KAAK,CAAC,CAAD,CAAL,CAAS,CAAT,CAAf,EAA4BG,WAA5B,EAAP;AACH,KAFD,MAEO,IAAIH,KAAK,CAACR,MAAN,KAAiB,CAArB,EAAwB;AAC3B,YAAMU,IAAI,GAAGF,KAAK,CAAC,CAAD,CAAlB;;AACA,UAAIE,IAAI,CAACV,MAAL,IAAe,CAAnB,EAAsB;AAClB,eAAOU,IAAI,CAACE,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqBD,WAArB,EAAP;AACH,OAFD,MAEO;AACH,eAAO,CAACD,IAAI,GAAG,GAAR,EAAaC,WAAb,EAAP;AACH;AACJ;;AAED,WAAO,IAAP;AACH,GArBD;;AAuBA,QAAME,gBAAgB,GAAGpB,YAAY,CAACgB,MAAb,CAAoB/B,IAAI,IAAI;AACjD,UAAM;AAAEa,MAAAA;AAAF,QAAWK,aAAa,CAAClB,IAAI,CAACa,IAAN,CAA9B;AACA,WAAOA,IAAI,KAAK,SAAhB;AACH,GAHwB,CAAzB;;AAKJ,QAAMuB,gBAAgB,GAAIC,QAAD,IAAc;AACnCC,IAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCF,QAAzC,EADmC,CAGnC;;AACA,QAAIG,SAAJ;;AACA,QAAI,OAAOH,QAAP,KAAoB,QAAxB,EAAkC;AAC9BG,MAAAA,SAAS,GAAGH,QAAQ,CAACI,QAAT,CAAkB,GAAlB,IAAyBJ,QAAQ,CAAChB,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAzB,GAAkDgB,QAA9D;AACH,KAFD,MAEO,IAAIA,QAAQ,IAAIA,QAAQ,CAACK,IAAzB,EAA+B;AAClCF,MAAAA,SAAS,GAAGH,QAAQ,CAACK,IAAT,CAAcD,QAAd,CAAuB,GAAvB,IAA8BJ,QAAQ,CAACK,IAAT,CAAcrB,KAAd,CAAoB,GAApB,EAAyB,CAAzB,CAA9B,GAA4DgB,QAAQ,CAACK,IAAjF;AACH,KAFM,MAEA;AACHJ,MAAAA,OAAO,CAACK,KAAR,CAAc,iCAAd,EAAiDN,QAAjD;AACA;AACH;;AAEDC,IAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+BC,SAAU,EAAtD;AACAnE,IAAAA,sBAAsB,CAACmE,SAAD,CAAtB;AACA7D,IAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACH,GAjBD;;AAqBA,QAAMiE,UAAU,GAAIC,UAAD,IAAgB;AAC/BP,IAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCM,UAApC;AACA,UAAMH,IAAI,GAAG,IAAII,IAAJ,CAASD,UAAT,CAAb;AACAP,IAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0CG,IAA1C;AACAJ,IAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAA8CG,IAAI,CAACK,iBAAL,EAA9C;AAEA,UAAMC,SAAS,GAAGN,IAAI,CAACO,kBAAL,CAAwB,OAAxB,EAAiC;AAC/CC,MAAAA,IAAI,EAAE,SADyC;AAE/CC,MAAAA,KAAK,EAAE,OAFwC;AAG/CC,MAAAA,GAAG,EAAE;AAH0C,KAAjC,CAAlB;AAMAd,IAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCS,SAArC;AACA,WAAOA,SAAP;AACH,GAdD;;AAiBI,QAAMK,eAAe,GAAIR,UAAD,IAAgB;AACpC,UAAMH,IAAI,GAAG,IAAII,IAAJ,CAASD,UAAT,CAAb;AACA,WAAOH,IAAI,CAACO,kBAAL,CAAwB,OAAxB,EAAiC;AACpCE,MAAAA,KAAK,EAAE,OAD6B;AAEpCC,MAAAA,GAAG,EAAE;AAF+B,KAAjC,CAAP;AAIH,GAND;;AAQA,sBACI;AAAK,IAAA,SAAS,EAAC,oBAAf;AAAoC,IAAA,KAAK,EAAE;AACvCE,MAAAA,YAAY,EAAE,MADyB;AAEvCC,MAAAA,KAAK,EAAE,MAFgC;AAGvCC,MAAAA,QAAQ,EAAE,OAH6B;AAIvCC,MAAAA,UAAU,EAAE,CAJ2B;AAKvCC,MAAAA,MAAM,EAAE;AAL+B,KAA3C;AAAA,4BAQI;AAAK,MAAA,SAAS,EAAC,mBAAf;AAAmC,MAAA,KAAK,EAAE;AAAEC,QAAAA,eAAe,EAAE,SAAnB;AAA8BL,QAAAA,YAAY,EAAE;AAA5C,OAA1C;AAAA,6BACI;AAAK,QAAA,SAAS,EAAC,mDAAf;AAAA,gCACI;AAAI,UAAA,SAAS,EAAC,kBAAd;AAAiC,UAAA,KAAK,EAAE;AAAEM,YAAAA,KAAK,EAAE;AAAT,WAAxC;AAAA,oBACK5F,cAAc,GAAG,kBAAH,GAAwB;AAD3C;AAAA;AAAA;AAAA;AAAA,gBADJ,eAII;AAAK,UAAA,SAAS,EAAC,2BAAf;AAAA,qBACK,CAACA,cAAD,iBACG;AAAA,oCACI;AAAK,cAAA,SAAS,EAAG,uBAAsBiD,WAAW,GAAG,YAAH,GAAkB,WAAY,EAAhF;AACK,cAAA,KAAK,EAAE;AAAEsC,gBAAAA,KAAK,EAAE,KAAT;AAAgBM,gBAAAA,MAAM,EAAE;AAAxB,eADZ;AAEK,cAAA,KAAK,EAAE5C,WAAW,GAAG,WAAH,GAAiB;AAFxC;AAAA;AAAA;AAAA;AAAA,oBADJ,eAKI;AAAM,cAAA,SAAS,EAAC,YAAhB;AAA6B,cAAA,KAAK,EAAE;AAAE6C,gBAAAA,QAAQ,EAAE;AAAZ,eAApC;AAAA,wBACK7C,WAAW,GAAG,MAAH,GAAY;AAD5B;AAAA;AAAA;AAAA;AAAA,oBALJ;AAAA,0BAFR,EAYKjD,cAAc,iBACX;AAAM,YAAA,SAAS,EAAC,YAAhB;AAA6B,YAAA,KAAK,EAAE;AAAE8F,cAAAA,QAAQ,EAAE;AAAZ,aAApC;AAAA,sBACKT,eAAe,CAACpF,YAAD;AADpB;AAAA;AAAA;AAAA;AAAA,kBAbR;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,YARJ,eAkCI;AAAK,MAAA,SAAS,EAAC,KAAf;AAAA,8BAEI;AAAK,QAAA,SAAS,EAAC,aAAf;AAA6B,QAAA,KAAK,EAAE;AAAE8F,UAAAA,GAAG,EAAE;AAAP,SAApC;AAAA,gCACI;AACI,UAAA,SAAS,EAAG,cAAa,CAAC/F,cAAD,GAAkB,aAAlB,GAAkC,qBAAsB,EADrF;AAEI,UAAA,KAAK,EAAE;AACHsF,YAAAA,YAAY,EAAE,KADX;AAEHQ,YAAAA,QAAQ,EAAE,MAFP;AAGHE,YAAAA,UAAU,EAAE,KAHT;AAIHC,YAAAA,IAAI,EAAE;AAJH,WAFX;AAQI,UAAA,OAAO,EAAE3F,oBARb;AASI,UAAA,QAAQ,EAAEC,OATd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eAcI;AAAK,UAAA,SAAS,EAAC,yCAAf;AAAyD,UAAA,KAAK,EAAE;AAAE0F,YAAAA,IAAI,EAAE;AAAR,WAAhE;AAAA,kCACI;AACI,YAAA,SAAS,EAAG,oBAAmBjG,cAAc,GAAG,aAAH,GAAmB,qBAAsB,EAD1F;AAEI,YAAA,KAAK,EAAE;AACHsF,cAAAA,YAAY,EAAE,KADX;AAEHQ,cAAAA,QAAQ,EAAE,MAFP;AAGHE,cAAAA,UAAU,EAAE;AAHT,aAFX;AAOI,YAAA,OAAO,EAAE,MAAMrF,iBAAiB,CAAC,CAACD,cAAF,CAPpC;AAQI,YAAA,QAAQ,EAAEH,OARd;AAAA,sBAUKA,OAAO,GAAG,YAAH,GAAkB;AAV9B;AAAA;AAAA;AAAA;AAAA,kBADJ,EAeKG,cAAc,IAAI,CAACH,OAAnB,iBACG;AAAK,YAAA,SAAS,EAAC,mBAAf;AAAmC,YAAA,KAAK,EAAE;AACtC2F,cAAAA,GAAG,EAAE,MADiC;AAEtCC,cAAAA,IAAI,EAAE,CAFgC;AAGtCC,cAAAA,KAAK,EAAE,CAH+B;AAItCC,cAAAA,MAAM,EAAE,IAJ8B;AAKtCV,cAAAA,eAAe,EAAE,OALqB;AAMtCD,cAAAA,MAAM,EAAE,mBAN8B;AAOtCJ,cAAAA,YAAY,EAAE,KAPwB;AAQtCgB,cAAAA,SAAS,EAAE,8BAR2B;AAStCC,cAAAA,SAAS,EAAE,OAT2B;AAUtCC,cAAAA,SAAS,EAAE,MAV2B;AAWtCC,cAAAA,SAAS,EAAE;AAX2B,aAA1C;AAAA,oCAaI;AAAK,cAAA,SAAS,EAAC,yBAAf;AAAyC,cAAA,KAAK,EAAE;AAC5Cd,gBAAAA,eAAe,EAAE,SAD2B;AAE5CG,gBAAAA,QAAQ,EAAE,MAFkC;AAG5CE,gBAAAA,UAAU,EAAE,KAHgC;AAI5CJ,gBAAAA,KAAK,EAAE;AAJqC,eAAhD;AAAA,0CAMkBzF,cAAc,CAACmD,MANjC;AAAA;AAAA;AAAA;AAAA;AAAA,oBAbJ,EAsBKnD,cAAc,CAACmD,MAAf,GAAwB,CAAxB,GACGnD,cAAc,CAAC4B,GAAf,CAAmB,CAACsC,QAAD,EAAWqC,KAAX,kBAGf;AAEI,cAAA,SAAS,EAAC,WAFd;AAGI,cAAA,KAAK,EAAE;AACHC,gBAAAA,MAAM,EAAE,SADL;AAEHb,gBAAAA,QAAQ,EAAE,MAFP;AAGHc,gBAAAA,YAAY,EAAEF,KAAK,GAAGvG,cAAc,CAACmD,MAAf,GAAwB,CAAhC,GAAoC,mBAApC,GAA0D,MAHrE;AAIHqC,gBAAAA,eAAe,EAAE1F,YAAY,KAAKoE,QAAQ,CAACK,IAA1B,GAAiC,SAAjC,GAA6C;AAJ3D,eAHX;AASI,cAAA,YAAY,EAAGmC,CAAD,IAAO;AACjB,oBAAI5G,YAAY,KAAKoE,QAAQ,CAACK,IAA9B,EAAoC;AAChCmC,kBAAAA,CAAC,CAAC3F,MAAF,CAAS4F,KAAT,CAAenB,eAAf,GAAiC,SAAjC;AACH;AACJ,eAbL;AAcI,cAAA,YAAY,EAAGkB,CAAD,IAAO;AACjB,oBAAI5G,YAAY,KAAKoE,QAAQ,CAACK,IAA9B,EAAoC;AAChCmC,kBAAAA,CAAC,CAAC3F,MAAF,CAAS4F,KAAT,CAAenB,eAAf,GAAiC,aAAjC;AACH;AACJ,eAlBL;AAmBI,cAAA,OAAO,EAAE,MAAMvB,gBAAgB,CAACC,QAAQ,CAACK,IAAV,CAnBnC;AAAA,qCAqBI;AAAK,gBAAA,SAAS,EAAC,mDAAf;AAAA,wCACI;AAAA,0CACI;AAAK,oBAAA,SAAS,EAAC,aAAf;AAAA,8BAA8BE,UAAU,CAACP,QAAQ,CAACK,IAAV;AAAxC;AAAA;AAAA;AAAA;AAAA,0BADJ,eAEI;AAAK,oBAAA,SAAS,EAAC,YAAf;AAA4B,oBAAA,KAAK,EAAE;AAAEoB,sBAAAA,QAAQ,EAAE;AAAZ,qBAAnC;AAAA,+BACKzB,QAAQ,CAAC1B,UADd;AAAA;AAAA;AAAA;AAAA;AAAA,0BAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBADJ,EAOK1C,YAAY,KAAKoE,QAAQ,CAACK,IAA1B,iBACG;AAAM,kBAAA,KAAK,EAAE;AAAEkB,oBAAAA,KAAK,EAAE,SAAT;AAAoBE,oBAAAA,QAAQ,EAAE;AAA9B,mBAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBARR;AAAA;AAAA;AAAA;AAAA;AAAA;AArBJ,eACSY,KADT;AAAA;AAAA;AAAA;AAAA,oBAHJ,CADH,gBAuCG;AAAK,cAAA,SAAS,EAAC,kCAAf;AAAkD,cAAA,KAAK,EAAE;AAAEZ,gBAAAA,QAAQ,EAAE;AAAZ,eAAzD;AAAA,sCACI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADJ,eAEI;AAAK,gBAAA,SAAS,EAAC,MAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBA7DR;AAAA;AAAA;AAAA;AAAA;AAAA,kBAhBR;AAAA;AAAA;AAAA;AAAA;AAAA,gBAdJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAFJ,eAwGI;AAAK,QAAA,SAAS,EAAC,kBAAf;AAAA,gCACI;AAAK,UAAA,SAAS,EAAC,uDAAf;AAAA,iCACI;AAAK,YAAA,SAAS,EAAC,UAAf;AACK,YAAA,KAAK,EAAE;AACHiB,cAAAA,UAAU,EAAE/G,cAAc,GACpB,2CADoB,GAEpB,2CAHH;AAIHsF,cAAAA,YAAY,EAAE,KAJX;AAKHC,cAAAA,KAAK,EAAE,MALJ;AAMHM,cAAAA,MAAM,EAAE,MANL;AAOHmB,cAAAA,OAAO,EAAE,MAPN;AAQHC,cAAAA,cAAc,EAAE,QARb;AASHC,cAAAA,UAAU,EAAE,QATT;AAUHZ,cAAAA,SAAS,EAAEtG,cAAc,GACnB,mCADmB,GAEnB,kCAZH;AAaHmH,cAAAA,UAAU,EAAE;AAbT,aADZ;AAAA,mCAgBI;AAAM,cAAA,SAAS,EAAC,yBAAhB;AAAA,wBACK5G,OAAO,GAAG,KAAH,GAAWyC;AADvB;AAAA;AAAA;AAAA;AAAA;AAhBJ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,gBADJ,eAuBI;AAAG,UAAA,SAAS,EAAC,iBAAb;AAA+B,UAAA,KAAK,EAAE;AAAE8C,YAAAA,QAAQ,EAAE,MAAZ;AAAoBE,YAAAA,UAAU,EAAE;AAAhC,WAAtC;AAAA,oBACKhG,cAAc,GACRC,YAAY,GAAI,YAAW2E,UAAU,CAAC3E,YAAD,CAAe,EAAxC,GAA4C,kBADhD,GAET;AAHV;AAAA;AAAA;AAAA;AAAA,gBAvBJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAxGJ,eAwII;AAAK,QAAA,SAAS,EAAC,aAAf;AAA6B,QAAA,KAAK,EAAE;AAAE0F,UAAAA,eAAe,EAAE,SAAnB;AAA8BL,UAAAA,YAAY,EAAE,KAA5C;AAAmD8B,UAAAA,OAAO,EAAE;AAA5D,SAApC;AAAA,gCACI;AACI,UAAA,SAAS,EAAG,qBAAoB5G,SAAS,KAAK,SAAd,GAA0B,aAA1B,GAA0C,WAAY,EAD1F;AAEI,UAAA,KAAK,EAAE;AACH8E,YAAAA,YAAY,EAAE,KADX;AAEHQ,YAAAA,QAAQ,EAAE,MAFP;AAGHE,YAAAA,UAAU,EAAE,KAHT;AAIHN,YAAAA,MAAM,EAAE,MAJL;AAKHyB,YAAAA,UAAU,EAAE;AALT,WAFX;AASI,UAAA,OAAO,EAAE,MAAM1G,YAAY,CAAC,SAAD,CAT/B;AAUI,UAAA,QAAQ,EAAEF,OAVd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADJ,eAeI;AACI,UAAA,SAAS,EAAG,qBAAoBC,SAAS,KAAK,MAAd,GAAuB,aAAvB,GAAuC,WAAY,EADvF;AAEI,UAAA,KAAK,EAAE;AACH8E,YAAAA,YAAY,EAAE,KADX;AAEHQ,YAAAA,QAAQ,EAAE,MAFP;AAGHE,YAAAA,UAAU,EAAE,KAHT;AAIHN,YAAAA,MAAM,EAAE,MAJL;AAKH2B,YAAAA,UAAU,EAAE,KALT;AAMHF,YAAAA,UAAU,EAAE;AANT,WAFX;AAUI,UAAA,OAAO,EAAE,MAAM1G,YAAY,CAAC,MAAD,CAV/B;AAWI,UAAA,QAAQ,EAAEF,OAXd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAfJ;AAAA;AAAA;AAAA;AAAA;AAAA,cAxIJ,eAyKI;AAAK,QAAA,KAAK,EAAE;AAAEgG,UAAAA,SAAS,EAAE,OAAb;AAAsBC,UAAAA,SAAS,EAAE;AAAjC,SAAZ;AAAA,kBACKjG,OAAO,gBACJ;AAAK,UAAA,SAAS,EAAC,kBAAf;AAAA,kCACI;AAAK,YAAA,SAAS,EAAC,+CAAf;AAA+D,YAAA,IAAI,EAAC,QAApE;AAA6E,YAAA,KAAK,EAAE;AAAEuF,cAAAA,QAAQ,EAAE;AAAZ,aAApF;AAAA,mCACI;AAAM,cAAA,SAAS,EAAC,iBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,kBADJ,eAII;AAAK,YAAA,SAAS,EAAC,iBAAf;AAAiC,YAAA,KAAK,EAAE;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAAxC;AAAA,mCACa9F,cAAc,GAAG,YAAH,GAAkB,MAD7C;AAAA;AAAA;AAAA;AAAA;AAAA,kBAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBADI,gBAUJ;AAAA,oBACKQ,SAAS,KAAK,SAAd,gBACG;AAAA,sBACKsC,eAAe,CAACQ,MAAhB,GAAyB,CAAzB,GACGR,eAAe,CAACf,GAAhB,CAAoB,CAACC,IAAD,EAAO0E,KAAP,kBAChB;AACK,cAAA,SAAS,EAAC,4DADf;AAEK,cAAA,KAAK,EAAE;AACHf,gBAAAA,eAAe,EAAE,MADd;AAEHL,gBAAAA,YAAY,EAAE,KAFX;AAGHI,gBAAAA,MAAM,EAAE,mBAHL;AAIHyB,gBAAAA,UAAU,EAAE,UAJT;AAKHR,gBAAAA,MAAM,EAAE;AALL,eAFZ;AASK,cAAA,YAAY,EAAGE,CAAD,IAAOA,CAAC,CAACS,aAAF,CAAgBR,KAAhB,CAAsBnB,eAAtB,GAAwC,SATlE;AAUK,cAAA,YAAY,EAAGkB,CAAD,IAAOA,CAAC,CAACS,aAAF,CAAgBR,KAAhB,CAAsBnB,eAAtB,GAAwC,MAVlE;AAAA,sCAWI;AAAK,gBAAA,SAAS,EAAC,2BAAf;AAAA,wCACI;AAAM,kBAAA,SAAS,EAAC,MAAhB;AAAuB,kBAAA,KAAK,EAAE;AAAEG,oBAAAA,QAAQ,EAAE;AAAZ,mBAA9B;AAAA,4BAAqD9D,IAAI,CAACuF;AAA1D;AAAA;AAAA;AAAA;AAAA,wBADJ,eAEI;AAAA,0CACI;AAAK,oBAAA,KAAK,EAAE;AAAEzB,sBAAAA,QAAQ,EAAE,MAAZ;AAAoBE,sBAAAA,UAAU,EAAE,KAAhC;AAAuCJ,sBAAAA,KAAK,EAAE;AAA9C,qBAAZ;AAAA,8BACK5D,IAAI,CAACU;AADV;AAAA;AAAA;AAAA;AAAA,0BADJ,eAII;AAAK,oBAAA,KAAK,EAAE;AAAEoD,sBAAAA,QAAQ,EAAE,MAAZ;AAAoBF,sBAAAA,KAAK,EAAE;AAA3B,qBAAZ;AAAA,+BACK5C,cAAc,GAAG,CAAjB,GAAqB,CAAEhB,IAAI,CAACW,UAAL,GAAkBK,cAAnB,GAAqC,GAAtC,EAA2CwE,OAA3C,CAAmD,CAAnD,CAArB,GAA6E,CADlF;AAAA;AAAA;AAAA;AAAA;AAAA,0BAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBAFJ;AAAA;AAAA;AAAA;AAAA;AAAA,sBAXJ,eAsBI;AAAK,gBAAA,SAAS,EAAC,2BAAf;AAAA,wCACI;AAAM,kBAAA,SAAS,EAAC,uBAAhB;AAAwC,kBAAA,KAAK,EAAE;AAAE1B,oBAAAA,QAAQ,EAAE;AAAZ,mBAA/C;AAAA,4BACK9D,IAAI,CAACW;AADV;AAAA;AAAA;AAAA;AAAA,wBADJ,eAII;AAAK,kBAAA,SAAS,EAAC,UAAf;AAA0B,kBAAA,KAAK,EAAE;AAAE4C,oBAAAA,KAAK,EAAE,MAAT;AAAiBM,oBAAAA,MAAM,EAAE;AAAzB,mBAAjC;AAAA,yCACI;AAAK,oBAAA,SAAS,EAAC,yBAAf;AACK,oBAAA,KAAK,EAAE;AACHN,sBAAAA,KAAK,EAAEvC,cAAc,GAAG,CAAjB,GAAsB,GAAGhB,IAAI,CAACW,UAAL,GAAkBK,cAAnB,GAAqC,GAAI,GAAjE,GAAsE,IAD1E;AAEHsC,sBAAAA,YAAY,EAAE;AAFX;AADZ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,wBAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,sBAtBJ;AAAA,eAAUoB,KAAV;AAAA;AAAA;AAAA;AAAA,oBADJ,CADH,gBAwCG;AAAK,cAAA,SAAS,EAAC,6BAAf;AAA6C,cAAA,KAAK,EAAE;AAAEZ,gBAAAA,QAAQ,EAAE;AAAZ,eAApD;AAAA,wBACK9F,cAAc,GACRC,YAAY,GAAI,uBAAsB2E,UAAU,CAAC3E,YAAD,CAAe,EAAnD,GAAuD,4BAD3D,GAERgD,WAAW,GAAG,2BAAH,GAAiC;AAHvD;AAAA;AAAA;AAAA;AAAA;AAzCR;AAAA;AAAA;AAAA;AAAA,kBADH,gBAmDG;AAAA,sBACKkB,gBAAgB,CAACb,MAAjB,GAA0B,CAA1B,GACGa,gBAAgB,CAACpC,GAAjB,CAAqB,CAACC,IAAD,EAAO0E,KAAP,KAAiB;AAClC,oBAAM;AAAE7D,gBAAAA,IAAF;AAAQH,gBAAAA;AAAR,kBAAoBQ,aAAa,CAAClB,IAAI,CAACa,IAAN,CAAvC;AACA,oBAAM4E,QAAQ,GAAGhE,mBAAmB,CAACZ,IAAD,CAApC;AACA,kCACI;AACK,gBAAA,SAAS,EAAC,4DADf;AAEK,gBAAA,KAAK,EAAE;AACH8C,kBAAAA,eAAe,EAAE,MADd;AAEHL,kBAAAA,YAAY,EAAE,KAFX;AAGHI,kBAAAA,MAAM,EAAE,mBAHL;AAIHyB,kBAAAA,UAAU,EAAE,UAJT;AAKHR,kBAAAA,MAAM,EAAE;AALL,iBAFZ;AASK,gBAAA,YAAY,EAAGE,CAAD,IAAOA,CAAC,CAACS,aAAF,CAAgBR,KAAhB,CAAsBnB,eAAtB,GAAwC,SATlE;AAUK,gBAAA,YAAY,EAAGkB,CAAD,IAAOA,CAAC,CAACS,aAAF,CAAgBR,KAAhB,CAAsBnB,eAAtB,GAAwC,MAVlE;AAAA,wCAWI;AAAK,kBAAA,SAAS,EAAC,2BAAf;AAAA,0CACI;AAAK,oBAAA,SAAS,EAAC,MAAf;AAAsB,oBAAA,KAAK,EAAE;AACzBJ,sBAAAA,KAAK,EAAE,MADkB;AAEzBM,sBAAAA,MAAM,EAAE,MAFiB;AAGzBF,sBAAAA,eAAe,EAAE,SAHQ;AAIzBL,sBAAAA,YAAY,EAAE,KAJW;AAKzB0B,sBAAAA,OAAO,EAAE,MALgB;AAMzBE,sBAAAA,UAAU,EAAE,QANa;AAOzBD,sBAAAA,cAAc,EAAE;AAPS,qBAA7B;AAAA,2CASI;AAAM,sBAAA,KAAK,EAAE;AACTnB,wBAAAA,QAAQ,EAAE,KADD;AAETE,wBAAAA,UAAU,EAAE,MAFH;AAGTJ,wBAAAA,KAAK,EAAE,SAHE;AAIT8B,wBAAAA,aAAa,EAAE;AAJN,uBAAb;AAAA,gCAMKD;AANL;AAAA;AAAA;AAAA;AAAA;AATJ;AAAA;AAAA;AAAA;AAAA,0BADJ,eAmBI;AAAA,4CACI;AAAK,sBAAA,KAAK,EAAE;AAAE3B,wBAAAA,QAAQ,EAAE,MAAZ;AAAoBE,wBAAAA,UAAU,EAAE,KAAhC;AAAuCJ,wBAAAA,KAAK,EAAE;AAA9C,uBAAZ;AAAA,gCACK/C;AADL;AAAA;AAAA;AAAA;AAAA,4BADJ,eAII;AAAK,sBAAA,KAAK,EAAE;AAAEiD,wBAAAA,QAAQ,EAAE,MAAZ;AAAoBF,wBAAAA,KAAK,EAAE;AAA3B,uBAAZ;AAAA,iCACKlD,OADL,cACiBM,cAAc,GAAG,CAAjB,GAAqB,CAAEhB,IAAI,CAACW,UAAL,GAAkBK,cAAnB,GAAqC,GAAtC,EAA2CwE,OAA3C,CAAmD,CAAnD,CAArB,GAA6E,CAD9F;AAAA;AAAA;AAAA;AAAA;AAAA,4BAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,0BAnBJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBAXJ,eAuCI;AAAK,kBAAA,SAAS,EAAC,2BAAf;AAAA,0CACI;AAAM,oBAAA,SAAS,EAAC,uBAAhB;AAAwC,oBAAA,KAAK,EAAE;AAAE1B,sBAAAA,QAAQ,EAAE;AAAZ,qBAA/C;AAAA,8BACK9D,IAAI,CAACW;AADV;AAAA;AAAA;AAAA;AAAA,0BADJ,eAII;AAAK,oBAAA,SAAS,EAAC,UAAf;AAA0B,oBAAA,KAAK,EAAE;AAAE4C,sBAAAA,KAAK,EAAE,MAAT;AAAiBM,sBAAAA,MAAM,EAAE;AAAzB,qBAAjC;AAAA,2CACI;AAAK,sBAAA,SAAS,EAAC,yBAAf;AACK,sBAAA,KAAK,EAAE;AACHN,wBAAAA,KAAK,EAAEvC,cAAc,GAAG,CAAjB,GAAsB,GAAGhB,IAAI,CAACW,UAAL,GAAkBK,cAAnB,GAAqC,GAAI,GAAjE,GAAsE,IAD1E;AAEHsC,wBAAAA,YAAY,EAAE;AAFX;AADZ;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,0BAJJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBAvCJ;AAAA,iBAAUoB,KAAV;AAAA;AAAA;AAAA;AAAA,sBADJ;AAuDH,aA1DD,CADH,gBA6DG;AAAK,cAAA,SAAS,EAAC,6BAAf;AAA6C,cAAA,KAAK,EAAE;AAAEZ,gBAAAA,QAAQ,EAAE;AAAZ,eAApD;AAAA,wBACK9F,cAAc,GACRC,YAAY,GAAI,oBAAmB2E,UAAU,CAAC3E,YAAD,CAAe,EAAhD,GAAoD,yBADxD,GAERgD,WAAW,GAAG,wBAAH,GAA8B;AAHpD;AAAA;AAAA;AAAA;AAAA;AA9DR;AAAA;AAAA;AAAA;AAAA;AApDR;AAXR;AAAA;AAAA;AAAA;AAAA,cAzKJ,eAoTI;AAAK,QAAA,SAAS,EAAC,sBAAf;AAAA,+BACI;AAAK,UAAA,SAAS,EAAC,2CAAf;AAA2D,UAAA,KAAK,EAAE;AAAE6C,YAAAA,QAAQ,EAAE;AAAZ,WAAlE;AAAA,kCACI;AAAA,uBAAOhD,eAAe,CAACQ,MAAvB;AAAA;AAAA;AAAA;AAAA;AAAA,kBADJ,eAEI;AAAA,uBAAOa,gBAAgB,CAACb,MAAxB;AAAA;AAAA;AAAA;AAAA;AAAA,kBAFJ,eAGI;AAAA,sBAAOtD,cAAc,GAAG,YAAH,GAAkB;AAAvC;AAAA;AAAA;AAAA;AAAA,kBAHJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADJ;AAAA;AAAA;AAAA;AAAA,cApTJ;AAAA;AAAA;AAAA;AAAA;AAAA,YAlCJ;AAAA;AAAA;AAAA;AAAA;AAAA,UADJ;AAiWH,CAhhBD;;GAAMF,gB;;KAAAA,gB;AAkhBN,eAAeA,gBAAf", "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { ChatState } from '../../context/AllProviders';\nimport { getCountryDataWithFlags } from '../../utils/countryFlags';\n\nconst OnlineUsersCount = () => {\n    const {\n        onlineUsersData,\n        historicalMode,\n        selectedDate,\n        historicalData,\n        availableDates,\n        fetchAvailableDates,\n        switchToHistoricalMode,\n        switchToRealTimeMode,\n        loading\n    } = ChatState();\n\n    const [activeTab, setActiveTab] = useState('country');\n    const [showDatePicker, setShowDatePicker] = useState(false);\n    const [selectedWebsite, setSelectedWebsite] = useState('all');\n    const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false);\n\n    // Fetch available dates on component mount\n    useEffect(() => {\n        fetchAvailableDates();\n    }, []);\n\n    // Close dropdowns when clicking outside\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (showDatePicker && !event.target.closest('.date-picker-container')) {\n                setShowDatePicker(false);\n            }\n            if (showWebsiteDropdown && !event.target.closest('.website-dropdown-container')) {\n                setShowWebsiteDropdown(false);\n            }\n        };\n\n        document.addEventListener('mousedown', handleClickOutside);\n        return () => {\n            document.removeEventListener('mousedown', handleClickOutside);\n        };\n    }, [showDatePicker, showWebsiteDropdown]);\n\n    // Determine which data to use based on mode\n    const currentData = historicalMode ? historicalData : onlineUsersData;\n\n    // ✅ CRITICAL FIX: Extract website breakdown data and create website options\n    const websiteBreakdown = currentData.websiteWidgetBreakdown || [];\n    const websiteOptions = [\n        { value: 'all', label: 'All Websites', count: currentData.totalOnline || 0 },\n        ...websiteBreakdown.map(item => ({\n            value: `${item.websiteUrl}|${item.widgetId}`,\n            label: item.websiteUrl || 'Unknown Website',\n            widgetId: item.widgetId,\n            count: item.userCount,\n            countries: item.countries || [],\n            cities: item.cities || []\n        }))\n    ];\n\n    // ✅ CRITICAL FIX: Filter data based on selected website\n    let filteredData = currentData;\n    if (selectedWebsite !== 'all') {\n        const selectedBreakdown = websiteBreakdown.find(item =>\n            `${item.websiteUrl}|${item.widgetId}` === selectedWebsite\n        );\n        if (selectedBreakdown) {\n            // Create filtered data structure for selected website\n            filteredData = {\n                ...currentData,\n                totalOnline: selectedBreakdown.userCount,\n                usersByCountry: selectedBreakdown.countries.map(country => ({\n                    country: country,\n                    user_count: 1 // This would need to be calculated properly from server\n                })),\n                usersByCity: selectedBreakdown.cities.map(city => ({\n                    city: city,\n                    user_count: 1 // This would need to be calculated properly from server\n                }))\n            };\n        }\n    }\n\n    const countryWiseData = getCountryDataWithFlags(filteredData.usersByCountry || []);\n    const cityWiseData = filteredData.usersByCity || [];\n    const totalLiveUsers = filteredData.totalOnline || 0;\n    const isConnected = historicalMode ? true : onlineUsersData.isConnected;\n\n    // Helper functions\n    const parseCityData = (cityString) => {\n        if (!cityString || cityString === 'Unknown') {\n            return { city: 'Unknown', country: 'Unknown' };\n        }\n        \n        const parts = cityString.split(', ');\n        if (parts.length >= 2) {\n            const city = parts[0];\n            const country = parts.slice(1).join(', ');\n            return { city, country };\n        }\n        \n        return { city: cityString, country: 'Unknown' };\n    };\n\n    const getCityAbbreviation = (cityName) => {\n        if (!cityName || cityName === 'Unknown') return 'UN';\n        \n        const cleanedCity = cityName\n            .replace(/\\b(City|Town|Village|Municipality)\\b/gi, '')\n            .trim();\n        \n        const words = cleanedCity.split(' ').filter(word => word.length > 0);\n        \n        if (words.length >= 2) {\n            return (words[0][0] + words[1][0]).toUpperCase();\n        } else if (words.length === 1) {\n            const word = words[0];\n            if (word.length >= 2) {\n                return word.substring(0, 2).toUpperCase();\n            } else {\n                return (word + 'X').toUpperCase();\n            }\n        }\n        \n        return 'UN';\n    };\n\n    const filteredCityData = cityWiseData.filter(item => {\n        const { city } = parseCityData(item.city);\n        return city !== 'Unknown';\n    });\n\nconst handleDateSelect = (dateInfo) => {\n    console.log('🔍 Raw dateInfo received:', dateInfo);\n    \n    // ✅ FIXED: Extract clean date regardless of format\n    let cleanDate;\n    if (typeof dateInfo === 'string') {\n        cleanDate = dateInfo.includes('T') ? dateInfo.split('T')[0] : dateInfo;\n    } else if (dateInfo && dateInfo.date) {\n        cleanDate = dateInfo.date.includes('T') ? dateInfo.date.split('T')[0] : dateInfo.date;\n    } else {\n        console.error('❌ Invalid date format received:', dateInfo);\n        return;\n    }\n    \n    console.log(`📅 User selected clean date: ${cleanDate}`);\n    switchToHistoricalMode(cleanDate);\n    setShowDatePicker(false);\n};\n\n\n\nconst formatDate = (dateString) => {\n    console.log('🔍 formatDate input:', dateString);\n    const date = new Date(dateString);\n    console.log('🔍 formatDate parsed date:', date);\n    console.log('🔍 formatDate timezone offset:', date.getTimezoneOffset());\n    \n    const formatted = date.toLocaleDateString('en-US', { \n        year: 'numeric', \n        month: 'short', \n        day: 'numeric' \n    });\n    \n    console.log('🔍 formatDate output:', formatted);\n    return formatted;\n};\n\n\n    const formatDateShort = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', { \n            month: 'short', \n            day: 'numeric' \n        });\n    };\n\n    return (\n        <div className=\"bg-white shadow-sm\" style={{ \n            borderRadius: \"12px\", \n            width: \"100%\", \n            maxWidth: \"320px\", \n            flexShrink: 0,\n            border: \"1px solid #e9ecef\"\n        }}>\n            {/* Header */}\n            <div className=\"p-3 border-bottom\" style={{ backgroundColor: \"#f8f9fa\", borderRadius: \"12px 12px 0 0\" }}>\n                <div className=\"d-flex justify-content-between align-items-center\">\n                    <h6 className=\"mb-0 fw-semibold\" style={{ color: \"#495057\" }}>\n                        {historicalMode ? 'Historical Users' : 'Live Users'}\n                    </h6>\n                    <div className=\"d-flex align-items-center\">\n                        {!historicalMode && (\n                            <>\n                                <div className={`rounded-circle me-2 ${isConnected ? 'bg-success' : 'bg-danger'}`}\n                                     style={{ width: \"8px\", height: \"8px\" }}\n                                     title={isConnected ? 'Connected' : 'Disconnected'}>\n                                </div>\n                                <span className=\"text-muted\" style={{ fontSize: \"12px\" }}>\n                                    {isConnected ? 'Live' : 'Offline'}\n                                </span>\n                            </>\n                        )}\n                        {historicalMode && (\n                            <span className=\"text-muted\" style={{ fontSize: \"12px\" }}>\n                                {formatDateShort(selectedDate)}\n                            </span>\n                        )}\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"p-3\">\n                {/* Mode Switch Buttons */}\n                <div className=\"d-flex mb-3\" style={{ gap: \"8px\" }}>\n                    <button \n                        className={`btn btn-sm ${!historicalMode ? 'btn-success' : 'btn-outline-success'}`}\n                        style={{ \n                            borderRadius: \"6px\",\n                            fontSize: \"11px\",\n                            fontWeight: \"600\",\n                            flex: 1\n                        }}\n                        onClick={switchToRealTimeMode}\n                        disabled={loading}\n                    >\n                        Live\n                    </button>\n                    <div className=\"position-relative date-picker-container\" style={{ flex: 1 }}>\n                        <button \n                            className={`btn btn-sm w-100 ${historicalMode ? 'btn-primary' : 'btn-outline-primary'}`}\n                            style={{ \n                                borderRadius: \"6px\",\n                                fontSize: \"11px\",\n                                fontWeight: \"600\"\n                            }}\n                            onClick={() => setShowDatePicker(!showDatePicker)}\n                            disabled={loading}\n                        >\n                            {loading ? 'Loading...' : 'History ▼'}\n                        </button>\n                        \n                        {/* Date Picker Dropdown */}\n                        {showDatePicker && !loading && (\n                            <div className=\"position-absolute\" style={{\n                                top: \"100%\",\n                                left: 0,\n                                right: 0,\n                                zIndex: 1000,\n                                backgroundColor: \"white\",\n                                border: \"1px solid #dee2e6\",\n                                borderRadius: \"8px\",\n                                boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n                                maxHeight: \"250px\",\n                                overflowY: \"auto\",\n                                marginTop: \"4px\"\n                            }}>\n                                <div className=\"px-3 py-2 border-bottom\" style={{\n                                    backgroundColor: \"#f8f9fa\",\n                                    fontSize: \"11px\",\n                                    fontWeight: \"600\",\n                                    color: \"#6c757d\"\n                                }}>\n                                    Select Date ({availableDates.length} days available)\n                                </div>\n                                \n                                {availableDates.length > 0 ? (\n                                    availableDates.map((dateInfo, index) => (\n\n                                        \n                                        <div\n                                            key={index}\n                                            className=\"px-3 py-2\"\n                                            style={{\n                                                cursor: \"pointer\",\n                                                fontSize: \"12px\",\n                                                borderBottom: index < availableDates.length - 1 ? \"1px solid #f1f3f4\" : \"none\",\n                                                backgroundColor: selectedDate === dateInfo.date ? \"#e3f2fd\" : \"transparent\"\n                                            }}\n                                            onMouseEnter={(e) => {\n                                                if (selectedDate !== dateInfo.date) {\n                                                    e.target.style.backgroundColor = \"#f8f9fa\";\n                                                }\n                                            }}\n                                            onMouseLeave={(e) => {\n                                                if (selectedDate !== dateInfo.date) {\n                                                    e.target.style.backgroundColor = \"transparent\";\n                                                }\n                                            }}\n                                            onClick={() => handleDateSelect(dateInfo.date)}\n                                        >\n                                            <div className=\"d-flex justify-content-between align-items-center\">\n                                                <div>\n                                                    <div className=\"fw-semibold\">{formatDate(dateInfo.date)}</div>\n                                                    <div className=\"text-muted\" style={{ fontSize: \"10px\" }}>\n                                                        {dateInfo.user_count} unique users\n                                                    </div>\n                                                </div>\n                                                {selectedDate === dateInfo.date && (\n                                                    <span style={{ color: \"#1976d2\", fontSize: \"10px\" }}>✓</span>\n                                                )}\n                                            </div>\n                                        </div>\n                                    ))\n                                ) : (\n                                    <div className=\"px-3 py-4 text-muted text-center\" style={{ fontSize: \"12px\" }}>\n                                        <div>📅</div>\n                                        <div className=\"mt-2\">No historical data available</div>\n                                    </div>\n                                )}\n                            </div>\n                        )}\n                    </div>\n                </div>\n\n                {/* Total Users Display */}\n                <div className=\"text-center mb-3\">\n                    <div className=\"d-flex justify-content-center align-items-center mb-2\">\n                        <div className=\"bg-green\" \n                             style={{ \n                                 background: historicalMode \n                                     ? \"linear-gradient(135deg, #28a745, #20c997)\" \n                                     : \"linear-gradient(135deg, #28a745, #20c997)\",\n                                 borderRadius: \"50%\",\n                                 width: \"60px\", \n                                 height: \"60px\",\n                                 display: \"flex\",\n                                 justifyContent: \"center\",\n                                 alignItems: \"center\",\n                                 boxShadow: historicalMode \n                                     ? \"0 4px 8px rgba(111, 66, 193, 0.3)\"\n                                     : \"0 4px 8px rgba(40, 167, 69, 0.3)\",\n                                 transition: \"all 0.3s ease\"\n                             }}>\n                            <span className=\"text-white fw-bold fs-4\">\n                                {loading ? '...' : totalLiveUsers}\n                            </span>\n                        </div>\n                    </div>\n                    <p className=\"text-muted mb-0\" style={{ fontSize: \"13px\", fontWeight: \"500\" }}>\n                        {historicalMode \n                            ? (selectedDate ? `Users on ${formatDate(selectedDate)}` : 'Historical Users')\n                            : 'Total Active Users'\n                        }\n                    </p>\n                </div>\n\n                {/* Tab Navigation */}\n                <div className=\"d-flex mb-3\" style={{ backgroundColor: \"#f8f9fa\", borderRadius: \"8px\", padding: \"4px\" }}>\n                    <button \n                        className={`flex-1 btn btn-sm ${activeTab === 'country' ? 'btn-primary' : 'btn-light'}`}\n                        style={{ \n                            borderRadius: \"6px\",\n                            fontSize: \"12px\",\n                            fontWeight: \"600\",\n                            border: \"none\",\n                            transition: \"all 0.2s\"\n                        }}\n                        onClick={() => setActiveTab('country')}\n                        disabled={loading}\n                    >\n                        Countries\n                    </button>\n                    <button \n                        className={`flex-1 btn btn-sm ${activeTab === 'city' ? 'btn-primary' : 'btn-light'}`}\n                        style={{ \n                            borderRadius: \"6px\",\n                            fontSize: \"12px\",\n                            fontWeight: \"600\",\n                            border: \"none\",\n                            marginLeft: \"4px\",\n                            transition: \"all 0.2s\"\n                        }}\n                        onClick={() => setActiveTab('city')}\n                        disabled={loading}\n                    >\n                        Cities\n                    </button>\n                </div>\n\n                {/* Content Area */}\n                <div style={{ maxHeight: \"200px\", overflowY: \"auto\" }}>\n                    {loading ? (\n                        <div className=\"text-center py-4\">\n                            <div className=\"spinner-border spinner-border-sm text-primary\" role=\"status\" style={{ fontSize: \"12px\" }}>\n                                <span className=\"visually-hidden\">Loading...</span>\n                            </div>\n                            <div className=\"text-muted mt-2\" style={{ fontSize: \"12px\" }}>\n                                Loading {historicalMode ? 'historical' : 'live'} data...\n                            </div>\n                        </div>\n                    ) : (\n                        <>\n                            {activeTab === 'country' ? (\n                                <div>\n                                    {countryWiseData.length > 0 ? (\n                                        countryWiseData.map((item, index) => (\n                                            <div key={index} \n                                                 className=\"d-flex justify-content-between align-items-center mb-2 p-2\"\n                                                 style={{\n                                                     backgroundColor: \"#fff\",\n                                                     borderRadius: \"8px\",\n                                                     border: \"1px solid #e9ecef\",\n                                                     transition: \"all 0.2s\",\n                                                     cursor: \"pointer\"\n                                                 }}\n                                                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = \"#f8f9fa\"}\n                                                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = \"#fff\"}>\n                                                <div className=\"d-flex align-items-center\">\n                                                    <span className=\"me-2\" style={{ fontSize: \"18px\" }}>{item.flag}</span>\n                                                    <div>\n                                                        <div style={{ fontSize: \"13px\", fontWeight: \"600\", color: \"#495057\" }}>\n                                                            {item.country}\n                                                        </div>\n                                                        <div style={{ fontSize: \"11px\", color: \"#6c757d\" }}>\n                                                            {totalLiveUsers > 0 ? ((item.user_count / totalLiveUsers) * 100).toFixed(1) : 0}%\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                                <div className=\"d-flex align-items-center\">\n                                                    <span className=\"badge bg-primary me-2\" style={{ fontSize: \"11px\" }}>\n                                                        {item.user_count}\n                                                    </span>\n                                                    <div className=\"progress\" style={{ width: \"40px\", height: \"4px\" }}>\n                                                        <div className=\"progress-bar bg-primary\"\n                                                             style={{\n                                                                 width: totalLiveUsers > 0 ? `${(item.user_count / totalLiveUsers) * 100}%` : '0%',\n                                                                 borderRadius: \"2px\"\n                                                             }}>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        ))\n                                    ) : (\n                                        <div className=\"text-center text-muted py-3\" style={{ fontSize: \"13px\" }}>\n                                            {historicalMode \n                                                ? (selectedDate ? `No country data for ${formatDate(selectedDate)}` : 'No historical country data') \n                                                : (isConnected ? 'No country data available' : 'Connecting...')\n                                            }\n                                        </div>\n                                    )}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredCityData.length > 0 ? (\n                                        filteredCityData.map((item, index) => {\n                                            const { city, country } = parseCityData(item.city);\n                                            const cityAbbr = getCityAbbreviation(city);\n                                            return (\n                                                <div key={index} \n                                                     className=\"d-flex justify-content-between align-items-center mb-2 p-2\"\n                                                     style={{\n                                                         backgroundColor: \"#fff\",\n                                                         borderRadius: \"8px\",\n                                                         border: \"1px solid #e9ecef\",\n                                                         transition: \"all 0.2s\",\n                                                         cursor: \"pointer\"\n                                                     }}\n                                                     onMouseEnter={(e) => e.currentTarget.style.backgroundColor = \"#f8f9fa\"}\n                                                     onMouseLeave={(e) => e.currentTarget.style.backgroundColor = \"#fff\"}>\n                                                    <div className=\"d-flex align-items-center\">\n                                                        <div className=\"me-2\" style={{\n                                                            width: \"20px\",\n                                                            height: \"20px\",\n                                                            backgroundColor: \"#e9ecef\",\n                                                            borderRadius: \"50%\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        }}>\n                                                            <span style={{ \n                                                                fontSize: \"8px\", \n                                                                fontWeight: \"bold\", \n                                                                color: \"#6c757d\",\n                                                                letterSpacing: \"-0.5px\"\n                                                            }}>\n                                                                {cityAbbr}\n                                                            </span>\n                                                        </div>\n                                                        <div>\n                                                            <div style={{ fontSize: \"13px\", fontWeight: \"600\", color: \"#495057\" }}>\n                                                                {city}\n                                                            </div>\n                                                            <div style={{ fontSize: \"11px\", color: \"#6c757d\" }}>\n                                                                {country} • {totalLiveUsers > 0 ? ((item.user_count / totalLiveUsers) * 100).toFixed(1) : 0}%\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                    <div className=\"d-flex align-items-center\">\n                                                        <span className=\"badge bg-primary me-2\" style={{ fontSize: \"11px\" }}>\n                                                            {item.user_count}\n                                                        </span>\n                                                        <div className=\"progress\" style={{ width: \"40px\", height: \"4px\" }}>\n                                                            <div className=\"progress-bar bg-primary\"\n                                                                 style={{\n                                                                     width: totalLiveUsers > 0 ? `${(item.user_count / totalLiveUsers) * 100}%` : '0%',\n                                                                     borderRadius: \"2px\"\n                                                                 }}>\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            );\n                                        })\n                                    ) : (\n                                        <div className=\"text-center text-muted py-3\" style={{ fontSize: \"13px\" }}>\n                                            {historicalMode \n                                                ? (selectedDate ? `No city data for ${formatDate(selectedDate)}` : 'No historical city data') \n                                                : (isConnected ? 'No city data available' : 'Connecting...')\n                                            }\n                                        </div>\n                                    )}\n                                </div>\n                            )}\n                        </>\n                    )}\n                </div>\n\n                {/* Footer Stats */}\n                <div className=\"mt-3 pt-2 border-top\">\n                    <div className=\"d-flex justify-content-between text-muted\" style={{ fontSize: \"11px\" }}>\n                        <span>{countryWiseData.length} Countries</span>\n                        <span>{filteredCityData.length} Cities</span>\n                        <span>{historicalMode ? 'Historical' : 'Live Updates'}</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default OnlineUsersCount;\n"]}, "metadata": {}, "sourceType": "module"}