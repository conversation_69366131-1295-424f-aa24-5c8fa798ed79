{"ast": null, "code": "var _jsxFileName = \"D:\\\\DataGen\\\\authkey-chat-new\\\\src\\\\components\\\\ReportSections\\\\PageAnalyticsDashboard.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useState, useEffect } from 'react';\nimport { ChatState } from '../../context/AllProviders';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst PageAnalyticsDashboard = () => {\n  _s();\n\n  const {\n    pageAnalyticsData,\n    onlineUsersData,\n    loading\n  } = ChatState();\n  const [refreshing, setRefreshing] = useState(false);\n  const [selectedWebsite, setSelectedWebsite] = useState('all');\n  const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false); // Utility functions\n\n  const formatNumber = num => {\n    const numValue = Number(num);\n    if (isNaN(numValue) || !isFinite(numValue)) return '0';\n    if (numValue >= 1000000) return (numValue / 1000000).toFixed(1) + 'M';\n    if (numValue >= 1000) return (numValue / 1000).toFixed(1) + 'K';\n    return numValue.toString();\n  };\n\n  const safeGetValue = function (value) {\n    let fallback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    const numValue = Number(value);\n    return isNaN(numValue) || !isFinite(numValue) ? fallback : numValue;\n  };\n\n  const getEventColor = eventType => {\n    const colors = {\n      'click': '#3B82F6',\n      'page_view': '#10B981',\n      'widget_open': '#8B5CF6',\n      'widget_close': '#8B5CF6',\n      'whatsapp_send_click': '#25D366',\n      'auto_widget_open': '#F59E0B',\n      'button_click': '#EF4444',\n      'link_click': '#06B6D4',\n      'scroll': '#84CC16',\n      'page_enter': '#10B981',\n      'page_exit': '#F97316'\n    };\n    return colors[eventType] || '#6B7280';\n  };\n\n  const formatEventName = eventType => {\n    const names = {\n      'whatsapp_send_click': 'WhatsApp Click',\n      'auto_widget_open': 'Auto Widget Open',\n      'widget_open': 'Widget Open',\n      'widget_close': 'Widget Close',\n      'button_click': 'Button Click',\n      'link_click': 'Link Click',\n      'page_view': 'Page View',\n      'page_enter': 'Page Enter',\n      'page_exit': 'Page Exit'\n    };\n    return names[eventType] || eventType.replace('_', ' ');\n  };\n\n  const ConnectionStatus = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex align-items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `rounded-circle ${onlineUsersData.isConnected ? 'bg-success' : 'bg-danger'}`,\n        style: {\n          width: '10px',\n          height: '10px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), onlineUsersData.isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute top-0 start-0 rounded-circle bg-success animate-ping\",\n        style: {\n          width: '10px',\n          height: '10px',\n          opacity: '0.75'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `fw-medium ${onlineUsersData.isConnected ? 'text-success' : 'text-danger'}`,\n      style: {\n        fontSize: '14px'\n      },\n      children: onlineUsersData.isConnected ? 'Live' : 'Offline'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n\n  const TopPages = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"position-relative overflow-hidden\",\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n      borderRadius: '20px',\n      border: '1px solid rgba(226, 232, 240, 0.8)',\n      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n      backdropFilter: 'blur(10px)',\n      height: '480px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-bottom\",\n      style: {\n        borderColor: 'rgba(226, 232, 240, 0.5)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-1 fw-bold\",\n            style: {\n              color: '#1e293b',\n              fontSize: '18px'\n            },\n            children: \"Active Pages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0 text-muted\",\n            style: {\n              fontSize: '13px'\n            },\n            children: \"Real-time user activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"badge d-flex align-items-center gap-1 px-3 py-2\",\n          style: {\n            background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n            border: 'none',\n            borderRadius: '12px',\n            color: 'white',\n            fontSize: '12px',\n            fontWeight: '600'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: (onlineUsersData.topPages || []).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"pages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      style: {\n        height: 'calc(100% - 80px)',\n        overflowY: 'auto'\n      },\n      children: (onlineUsersData.topPages || []).length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex flex-column gap-3\",\n        children: (onlineUsersData.topPages || []).slice(0, 6).map((page, index) => {\n          var _page$path;\n\n          const userCount = safeGetValue(page.users, 0);\n          const maxUsers = Math.max(...(onlineUsersData.topPages || []).map(p => safeGetValue(p.users, 0)), 1);\n          const widthPercentage = Math.min(100, userCount / maxUsers * 100);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"position-relative p-3 transition-all\",\n            style: {\n              background: 'rgba(255, 255, 255, 0.7)',\n              borderRadius: '16px',\n              border: '1px solid rgba(226, 232, 240, 0.6)',\n              backdropFilter: 'blur(8px)',\n              transition: 'all 0.3s ease',\n              cursor: 'pointer'\n            },\n            onMouseEnter: e => {\n              e.target.style.transform = 'translateY(-2px)';\n              e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\n            },\n            onMouseLeave: e => {\n              e.target.style.transform = 'translateY(0px)';\n              e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center justify-content-center\",\n                  style: {\n                    width: '32px',\n                    height: '32px',\n                    borderRadius: '10px',\n                    background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\n                    color: 'white',\n                    fontSize: '14px',\n                    fontWeight: '600'\n                  },\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 fw-semibold\",\n                    style: {\n                      fontSize: '14px',\n                      color: '#1e293b'\n                    },\n                    children: page.path === '/' ? 'Homepage' : (((_page$path = page.path) === null || _page$path === void 0 ? void 0 : _page$path.length) > 25 ? page.path.substring(0, 25) + '...' : page.path) || 'Unknown'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 text-muted\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [userCount, \" active \", userCount === 1 ? 'user' : 'users']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"badge d-flex align-items-center justify-content-center\",\n                style: {\n                  background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\n                  color: 'white',\n                  borderRadius: '12px',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  minWidth: '40px',\n                  height: '28px'\n                },\n                children: userCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative\",\n              style: {\n                height: '6px',\n                background: 'rgba(226, 232, 240, 0.5)',\n                borderRadius: '6px',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-absolute top-0 start-0 h-100\",\n                style: {\n                  width: `${widthPercentage}%`,\n                  background: `linear-gradient(90deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}cc)`,\n                  borderRadius: '6px',\n                  transition: 'width 0.5s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex flex-column align-items-center justify-content-center h-100 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3 d-flex align-items-center justify-content-center\",\n          style: {\n            width: '80px',\n            height: '80px',\n            borderRadius: '20px',\n            background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\n            color: '#64748b'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"32\",\n            height: \"32\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-2 fw-semibold\",\n          style: {\n            color: '#475569'\n          },\n          children: \"No Active Pages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0 text-muted\",\n          style: {\n            fontSize: '14px'\n          },\n          children: \"Page activity will appear here when users visit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n\n  const TopInteractions = () => {\n    var _pageAnalyticsData$be, _pageAnalyticsData$be2;\n\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-relative overflow-hidden\",\n      style: {\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n        borderRadius: '20px',\n        border: '1px solid rgba(226, 232, 240, 0.8)',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n        backdropFilter: 'blur(10px)',\n        height: '480px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-bottom\",\n        style: {\n          borderColor: 'rgba(226, 232, 240, 0.5)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-1 fw-bold\",\n              style: {\n                color: '#1e293b',\n                fontSize: '18px'\n              },\n              children: \"User Interactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-0 text-muted\",\n              style: {\n                fontSize: '13px'\n              },\n              children: \"Today's activity breakdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge d-flex align-items-center gap-1 px-3 py-2\",\n            style: {\n              background: 'linear-gradient(135deg, #10b981, #059669)',\n              border: 'none',\n              borderRadius: '12px',\n              color: 'white',\n              fontSize: '12px',\n              fontWeight: '600'\n            },\n            children: \"Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        style: {\n          height: 'calc(100% - 80px)',\n          overflowY: 'auto'\n        },\n        children: (((_pageAnalyticsData$be = pageAnalyticsData.behavior) === null || _pageAnalyticsData$be === void 0 ? void 0 : _pageAnalyticsData$be.interactions) || []).length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column gap-3\",\n          children: (((_pageAnalyticsData$be2 = pageAnalyticsData.behavior) === null || _pageAnalyticsData$be2 === void 0 ? void 0 : _pageAnalyticsData$be2.interactions) || []).slice(0, 7).map((interaction, index) => {\n            var _pageAnalyticsData$be3;\n\n            let count = safeGetValue(interaction.count, 0); // Divide page_exit count by 2 to fix double counting issue\n\n            if (interaction.event_type === 'page_exit') {\n              count = Math.round(count / 2);\n            }\n\n            const uniqueUsers = safeGetValue(interaction.unique_users, 0);\n            const maxCount = Math.max(...(((_pageAnalyticsData$be3 = pageAnalyticsData.behavior) === null || _pageAnalyticsData$be3 === void 0 ? void 0 : _pageAnalyticsData$be3.interactions) || []).map(i => safeGetValue(i.count, 0)), 1);\n            const widthPercentage = Math.min(100, count / maxCount * 100);\n            const eventColor = getEventColor(interaction.event_type);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative p-3 transition-all\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.7)',\n                borderRadius: '16px',\n                border: '1px solid rgba(226, 232, 240, 0.6)',\n                backdropFilter: 'blur(8px)',\n                transition: 'all 0.3s ease',\n                cursor: 'pointer'\n              },\n              onMouseEnter: e => {\n                e.target.style.transform = 'translateY(-2px)';\n                e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\n              },\n              onMouseLeave: e => {\n                e.target.style.transform = 'translateY(0px)';\n                e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center justify-content-center\",\n                    style: {\n                      width: '36px',\n                      height: '36px',\n                      borderRadius: '12px',\n                      background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\n                      color: 'white'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"m12 1 0 6m0 6 0 6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"m17 12-6 0m-6 0 6 0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 333,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-0 fw-semibold\",\n                      style: {\n                        fontSize: '14px',\n                        color: '#1e293b'\n                      },\n                      children: formatEventName(interaction.event_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-0 text-muted\",\n                      style: {\n                        fontSize: '12px'\n                      },\n                      children: [uniqueUsers, \" unique \", uniqueUsers === 1 ? 'user' : 'users']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"badge d-flex align-items-center justify-content-center\",\n                  style: {\n                    background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\n                    color: 'white',\n                    borderRadius: '12px',\n                    fontSize: '12px',\n                    fontWeight: '600',\n                    minWidth: '45px',\n                    height: '28px'\n                  },\n                  children: formatNumber(count)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-relative\",\n                style: {\n                  height: '6px',\n                  background: 'rgba(226, 232, 240, 0.5)',\n                  borderRadius: '6px',\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"position-absolute top-0 start-0 h-100\",\n                  style: {\n                    width: `${widthPercentage}%`,\n                    background: `linear-gradient(90deg, ${eventColor}, ${eventColor}cc)`,\n                    borderRadius: '6px',\n                    transition: 'width 0.5s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column align-items-center justify-content-center h-100 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 d-flex align-items-center justify-content-center\",\n            style: {\n              width: '80px',\n              height: '80px',\n              borderRadius: '20px',\n              background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\n              color: '#64748b'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"32\",\n              height: \"32\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.5\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-2 fw-semibold\",\n            style: {\n              color: '#475569'\n            },\n            children: \"No Interactions Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0 text-muted\",\n            style: {\n              fontSize: '14px'\n            },\n            children: \"User interactions will be displayed here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 5\n    }, this);\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 position-relative\",\n    style: {\n      background: 'transparent',\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4 p-4\",\n      style: {\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '20px',\n        backdropFilter: 'blur(20px)',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"mb-1 fw-bold\",\n          style: {\n            color: 'maroon',\n            fontSize: '24px'\n          },\n          children: \"Widget Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0 text-muted\",\n          style: {\n            fontSize: '14px'\n          },\n          children: \"Real-time insights and user behavior\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row g-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-xl-6\",\n        children: /*#__PURE__*/_jsxDEV(TopPages, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-xl-6\",\n        children: /*#__PURE__*/_jsxDEV(TopInteractions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this), (loading || pageAnalyticsData.loading) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n      style: {\n        zIndex: 9999,\n        background: 'rgba(0, 0, 0, 0.4)',\n        backdropFilter: 'blur(8px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-4\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: '20px',\n          backdropFilter: 'blur(20px)',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border mb-3\",\n          style: {\n            width: '3rem',\n            height: '3rem',\n            color: '#667eea'\n          },\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0 fw-medium\",\n          style: {\n            color: '#1e293b'\n          },\n          children: \"Updating Analytics...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .animate-ping {\n          animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n        }\n        \n        @keyframes ping {\n          75%, 100% {\n            transform: scale(2);\n            opacity: 0;\n          }\n        }\n        \n        .transition-all {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n        \n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: rgba(226, 232, 240, 0.3);\n          border-radius: 6px;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: rgba(148, 163, 184, 0.5);\n          border-radius: 6px;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: rgba(148, 163, 184, 0.7);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 412,\n    columnNumber: 5\n  }, this);\n};\n\n_s(PageAnalyticsDashboard, \"ZTtMjgTL9+42x1dpvyMakunwiVo=\");\n\n_c = PageAnalyticsDashboard;\nexport default PageAnalyticsDashboard;\n\nvar _c;\n\n$RefreshReg$(_c, \"PageAnalyticsDashboard\");", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/components/ReportSections/PageAnalyticsDashboard.jsx"], "names": ["React", "useState", "useEffect", "ChatState", "PageAnalyticsDashboard", "pageAnalyticsData", "onlineUsersData", "loading", "refreshing", "setRefreshing", "selectedWebsite", "setSelectedWebsite", "showWebsiteDropdown", "setShowWebsiteDropdown", "formatNumber", "num", "numValue", "Number", "isNaN", "isFinite", "toFixed", "toString", "safeGetValue", "value", "fallback", "getEventColor", "eventType", "colors", "formatEventName", "names", "replace", "ConnectionStatus", "isConnected", "width", "height", "opacity", "fontSize", "TopPages", "background", "borderRadius", "border", "boxShadow", "<PERSON><PERSON>ilter", "borderColor", "color", "fontWeight", "topPages", "length", "overflowY", "slice", "map", "page", "index", "userCount", "users", "maxUsers", "Math", "max", "p", "widthPercentage", "min", "transition", "cursor", "e", "target", "style", "transform", "path", "substring", "min<PERSON><PERSON><PERSON>", "overflow", "TopInteractions", "behavior", "interactions", "interaction", "count", "event_type", "round", "uniqueUsers", "unique_users", "maxCount", "i", "eventColor", "padding", "zIndex"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,QAA2C,OAA3C;AACA,SAASC,SAAT,QAA0B,4BAA1B;;;AAEA,MAAMC,sBAAsB,GAAG,MAAM;AAAA;;AACnC,QAAM;AACJC,IAAAA,iBADI;AAEJC,IAAAA,eAFI;AAGJC,IAAAA;AAHI,MAIFJ,SAAS,EAJb;AAMA,QAAM,CAACK,UAAD,EAAaC,aAAb,IAA8BR,QAAQ,CAAC,KAAD,CAA5C;AACA,QAAM,CAACS,eAAD,EAAkBC,kBAAlB,IAAwCV,QAAQ,CAAC,KAAD,CAAtD;AACA,QAAM,CAACW,mBAAD,EAAsBC,sBAAtB,IAAgDZ,QAAQ,CAAC,KAAD,CAA9D,CATmC,CAWnC;;AACA,QAAMa,YAAY,GAAIC,GAAD,IAAS;AAC5B,UAAMC,QAAQ,GAAGC,MAAM,CAACF,GAAD,CAAvB;AACA,QAAIG,KAAK,CAACF,QAAD,CAAL,IAAmB,CAACG,QAAQ,CAACH,QAAD,CAAhC,EAA4C,OAAO,GAAP;AAE5C,QAAIA,QAAQ,IAAI,OAAhB,EAAyB,OAAO,CAACA,QAAQ,GAAG,OAAZ,EAAqBI,OAArB,CAA6B,CAA7B,IAAkC,GAAzC;AACzB,QAAIJ,QAAQ,IAAI,IAAhB,EAAsB,OAAO,CAACA,QAAQ,GAAG,IAAZ,EAAkBI,OAAlB,CAA0B,CAA1B,IAA+B,GAAtC;AACtB,WAAOJ,QAAQ,CAACK,QAAT,EAAP;AACD,GAPD;;AASA,QAAMC,YAAY,GAAG,UAACC,KAAD,EAAyB;AAAA,QAAjBC,QAAiB,uEAAN,CAAM;AAC5C,UAAMR,QAAQ,GAAGC,MAAM,CAACM,KAAD,CAAvB;AACA,WAAOL,KAAK,CAACF,QAAD,CAAL,IAAmB,CAACG,QAAQ,CAACH,QAAD,CAA5B,GAAyCQ,QAAzC,GAAoDR,QAA3D;AACD,GAHD;;AAKA,QAAMS,aAAa,GAAIC,SAAD,IAAe;AACnC,UAAMC,MAAM,GAAG;AACb,eAAS,SADI;AAEb,mBAAa,SAFA;AAGb,qBAAe,SAHF;AAIb,sBAAgB,SAJH;AAKb,6BAAuB,SALV;AAMb,0BAAoB,SANP;AAOb,sBAAgB,SAPH;AAQb,oBAAc,SARD;AASb,gBAAU,SATG;AAUb,oBAAc,SAVD;AAWb,mBAAa;AAXA,KAAf;AAaA,WAAOA,MAAM,CAACD,SAAD,CAAN,IAAqB,SAA5B;AACD,GAfD;;AAiBA,QAAME,eAAe,GAAIF,SAAD,IAAe;AACrC,UAAMG,KAAK,GAAG;AACZ,6BAAuB,gBADX;AAEZ,0BAAoB,kBAFR;AAGZ,qBAAe,aAHH;AAIZ,sBAAgB,cAJJ;AAKZ,sBAAgB,cALJ;AAMZ,oBAAc,YANF;AAOZ,mBAAa,WAPD;AAQZ,oBAAc,YARF;AASZ,mBAAa;AATD,KAAd;AAWA,WAAOA,KAAK,CAACH,SAAD,CAAL,IAAoBA,SAAS,CAACI,OAAV,CAAkB,GAAlB,EAAuB,GAAvB,CAA3B;AACD,GAbD;;AAeA,QAAMC,gBAAgB,GAAG,mBACvB;AAAK,IAAA,SAAS,EAAC,iCAAf;AAAA,4BACE;AAAK,MAAA,SAAS,EAAC,mBAAf;AAAA,8BACE;AACE,QAAA,SAAS,EAAG,kBAAiBzB,eAAe,CAAC0B,WAAhB,GAA8B,YAA9B,GAA6C,WAAY,EADxF;AAEE,QAAA,KAAK,EAAE;AAAEC,UAAAA,KAAK,EAAE,MAAT;AAAiBC,UAAAA,MAAM,EAAE;AAAzB;AAFT;AAAA;AAAA;AAAA;AAAA,cADF,EAKG5B,eAAe,CAAC0B,WAAhB,iBACC;AACE,QAAA,SAAS,EAAC,wEADZ;AAEE,QAAA,KAAK,EAAE;AAAEC,UAAAA,KAAK,EAAE,MAAT;AAAiBC,UAAAA,MAAM,EAAE,MAAzB;AAAiCC,UAAAA,OAAO,EAAE;AAA1C;AAFT;AAAA;AAAA;AAAA;AAAA,cANJ;AAAA;AAAA;AAAA;AAAA;AAAA,YADF,eAaE;AAAM,MAAA,SAAS,EAAG,aAAY7B,eAAe,CAAC0B,WAAhB,GAA8B,cAA9B,GAA+C,aAAc,EAA3F;AAA8F,MAAA,KAAK,EAAE;AAAEI,QAAAA,QAAQ,EAAE;AAAZ,OAArG;AAAA,gBACG9B,eAAe,CAAC0B,WAAhB,GAA8B,MAA9B,GAAuC;AAD1C;AAAA;AAAA;AAAA;AAAA,YAbF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;;AAoBA,QAAMK,QAAQ,GAAG,mBACf;AACE,IAAA,SAAS,EAAC,mCADZ;AAEE,IAAA,KAAK,EAAE;AACLC,MAAAA,UAAU,EAAE,mDADP;AAELC,MAAAA,YAAY,EAAE,MAFT;AAGLC,MAAAA,MAAM,EAAE,oCAHH;AAILC,MAAAA,SAAS,EAAE,uEAJN;AAKLC,MAAAA,cAAc,EAAE,YALX;AAMLR,MAAAA,MAAM,EAAE;AANH,KAFT;AAAA,4BAYE;AAAK,MAAA,SAAS,EAAC,mBAAf;AAAmC,MAAA,KAAK,EAAE;AAAES,QAAAA,WAAW,EAAE;AAAf,OAA1C;AAAA,6BACE;AAAK,QAAA,SAAS,EAAC,mDAAf;AAAA,gCACE;AAAA,kCACE;AAAI,YAAA,SAAS,EAAC,cAAd;AAA6B,YAAA,KAAK,EAAE;AAAEC,cAAAA,KAAK,EAAE,SAAT;AAAoBR,cAAAA,QAAQ,EAAE;AAA9B,aAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eAIE;AAAG,YAAA,SAAS,EAAC,iBAAb;AAA+B,YAAA,KAAK,EAAE;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eASE;AACE,UAAA,SAAS,EAAC,iDADZ;AAEE,UAAA,KAAK,EAAE;AACLE,YAAAA,UAAU,EAAE,2CADP;AAELE,YAAAA,MAAM,EAAE,MAFH;AAGLD,YAAAA,YAAY,EAAE,MAHT;AAILK,YAAAA,KAAK,EAAE,OAJF;AAKLR,YAAAA,QAAQ,EAAE,MALL;AAMLS,YAAAA,UAAU,EAAE;AANP,WAFT;AAAA,kCAWE;AAAA,sBAAO,CAACvC,eAAe,CAACwC,QAAhB,IAA4B,EAA7B,EAAiCC;AAAxC;AAAA;AAAA;AAAA;AAAA,kBAXF,eAYE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAZF;AAAA;AAAA;AAAA;AAAA;AAAA,gBATF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,YAZF,eAwCE;AAAK,MAAA,SAAS,EAAC,KAAf;AAAqB,MAAA,KAAK,EAAE;AAAEb,QAAAA,MAAM,EAAE,mBAAV;AAA+Bc,QAAAA,SAAS,EAAE;AAA1C,OAA5B;AAAA,gBACG,CAAC1C,eAAe,CAACwC,QAAhB,IAA4B,EAA7B,EAAiCC,MAAjC,GAA0C,CAA1C,gBACC;AAAK,QAAA,SAAS,EAAC,0BAAf;AAAA,kBACG,CAACzC,eAAe,CAACwC,QAAhB,IAA4B,EAA7B,EAAiCG,KAAjC,CAAuC,CAAvC,EAA0C,CAA1C,EAA6CC,GAA7C,CAAiD,CAACC,IAAD,EAAOC,KAAP,KAAiB;AAAA;;AACjE,gBAAMC,SAAS,GAAG/B,YAAY,CAAC6B,IAAI,CAACG,KAAN,EAAa,CAAb,CAA9B;AACA,gBAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS,GAAG,CAACnD,eAAe,CAACwC,QAAhB,IAA4B,EAA7B,EAAiCI,GAAjC,CAAqCQ,CAAC,IAAIpC,YAAY,CAACoC,CAAC,CAACJ,KAAH,EAAU,CAAV,CAAtD,CAAZ,EAAiF,CAAjF,CAAjB;AACA,gBAAMK,eAAe,GAAGH,IAAI,CAACI,GAAL,CAAS,GAAT,EAAeP,SAAS,GAAGE,QAAb,GAAyB,GAAvC,CAAxB;AAEA,8BACE;AAEE,YAAA,SAAS,EAAC,sCAFZ;AAGE,YAAA,KAAK,EAAE;AACLjB,cAAAA,UAAU,EAAE,0BADP;AAELC,cAAAA,YAAY,EAAE,MAFT;AAGLC,cAAAA,MAAM,EAAE,oCAHH;AAILE,cAAAA,cAAc,EAAE,WAJX;AAKLmB,cAAAA,UAAU,EAAE,eALP;AAMLC,cAAAA,MAAM,EAAE;AANH,aAHT;AAWE,YAAA,YAAY,EAAGC,CAAD,IAAO;AACnBA,cAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAeC,SAAf,GAA2B,kBAA3B;AACAH,cAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAexB,SAAf,GAA2B,oCAA3B;AACD,aAdH;AAeE,YAAA,YAAY,EAAGsB,CAAD,IAAO;AACnBA,cAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAeC,SAAf,GAA2B,iBAA3B;AACAH,cAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAexB,SAAf,GAA2B,gCAA3B;AACD,aAlBH;AAAA,oCAoBE;AAAK,cAAA,SAAS,EAAC,wDAAf;AAAA,sCACE;AAAK,gBAAA,SAAS,EAAC,iCAAf;AAAA,wCACE;AACE,kBAAA,SAAS,EAAC,kDADZ;AAEE,kBAAA,KAAK,EAAE;AACLR,oBAAAA,KAAK,EAAE,MADF;AAELC,oBAAAA,MAAM,EAAE,MAFH;AAGLK,oBAAAA,YAAY,EAAE,MAHT;AAILD,oBAAAA,UAAU,EAAG,2BAA0Bb,aAAa,CAAC,WAAD,CAAc,KAAIA,aAAa,CAAC,WAAD,CAAc,KAJ5F;AAKLmB,oBAAAA,KAAK,EAAE,OALF;AAMLR,oBAAAA,QAAQ,EAAE,MANL;AAOLS,oBAAAA,UAAU,EAAE;AAPP,mBAFT;AAAA,4BAYGO,KAAK,GAAG;AAZX;AAAA;AAAA;AAAA;AAAA,wBADF,eAeE;AAAA,0CACE;AAAG,oBAAA,SAAS,EAAC,kBAAb;AAAgC,oBAAA,KAAK,EAAE;AAAEhB,sBAAAA,QAAQ,EAAE,MAAZ;AAAoBQ,sBAAAA,KAAK,EAAE;AAA3B,qBAAvC;AAAA,8BACGO,IAAI,CAACgB,IAAL,KAAc,GAAd,GAAoB,UAApB,GAAiC,CAAC,eAAAhB,IAAI,CAACgB,IAAL,0DAAWpB,MAAX,IAAoB,EAApB,GAAyBI,IAAI,CAACgB,IAAL,CAAUC,SAAV,CAAoB,CAApB,EAAuB,EAAvB,IAA6B,KAAtD,GAA8DjB,IAAI,CAACgB,IAApE,KAA6E;AADjH;AAAA;AAAA;AAAA;AAAA,0BADF,eAIE;AAAG,oBAAA,SAAS,EAAC,iBAAb;AAA+B,oBAAA,KAAK,EAAE;AAAE/B,sBAAAA,QAAQ,EAAE;AAAZ,qBAAtC;AAAA,+BACGiB,SADH,cACsBA,SAAS,KAAK,CAAd,GAAkB,MAAlB,GAA2B,OADjD;AAAA;AAAA;AAAA;AAAA;AAAA,0BAJF;AAAA;AAAA;AAAA;AAAA;AAAA,wBAfF;AAAA;AAAA;AAAA;AAAA;AAAA,sBADF,eAyBE;AACE,gBAAA,SAAS,EAAC,wDADZ;AAEE,gBAAA,KAAK,EAAE;AACLf,kBAAAA,UAAU,EAAG,2BAA0Bb,aAAa,CAAC,WAAD,CAAc,KAAIA,aAAa,CAAC,WAAD,CAAc,KAD5F;AAELmB,kBAAAA,KAAK,EAAE,OAFF;AAGLL,kBAAAA,YAAY,EAAE,MAHT;AAILH,kBAAAA,QAAQ,EAAE,MAJL;AAKLS,kBAAAA,UAAU,EAAE,KALP;AAMLwB,kBAAAA,QAAQ,EAAE,MANL;AAOLnC,kBAAAA,MAAM,EAAE;AAPH,iBAFT;AAAA,0BAYGmB;AAZH;AAAA;AAAA;AAAA;AAAA,sBAzBF;AAAA;AAAA;AAAA;AAAA;AAAA,oBApBF,eA8DE;AACE,cAAA,SAAS,EAAC,mBADZ;AAEE,cAAA,KAAK,EAAE;AACLnB,gBAAAA,MAAM,EAAE,KADH;AAELI,gBAAAA,UAAU,EAAE,0BAFP;AAGLC,gBAAAA,YAAY,EAAE,KAHT;AAIL+B,gBAAAA,QAAQ,EAAE;AAJL,eAFT;AAAA,qCASE;AACE,gBAAA,SAAS,EAAC,uCADZ;AAEE,gBAAA,KAAK,EAAE;AACLrC,kBAAAA,KAAK,EAAG,GAAE0B,eAAgB,GADrB;AAELrB,kBAAAA,UAAU,EAAG,0BAAyBb,aAAa,CAAC,WAAD,CAAc,KAAIA,aAAa,CAAC,WAAD,CAAc,KAF3F;AAGLc,kBAAAA,YAAY,EAAE,KAHT;AAILsB,kBAAAA,UAAU,EAAE;AAJP;AAFT;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA,oBA9DF;AAAA,aACOT,KADP;AAAA;AAAA;AAAA;AAAA,kBADF;AAoFD,SAzFA;AADH;AAAA;AAAA;AAAA;AAAA,cADD,gBA8FC;AAAK,QAAA,SAAS,EAAC,gFAAf;AAAA,gCACE;AACE,UAAA,SAAS,EAAC,uDADZ;AAEE,UAAA,KAAK,EAAE;AACLnB,YAAAA,KAAK,EAAE,MADF;AAELC,YAAAA,MAAM,EAAE,MAFH;AAGLK,YAAAA,YAAY,EAAE,MAHT;AAILD,YAAAA,UAAU,EAAE,2CAJP;AAKLM,YAAAA,KAAK,EAAE;AALF,WAFT;AAAA,iCAUE;AAAK,YAAA,KAAK,EAAC,IAAX;AAAgB,YAAA,MAAM,EAAC,IAAvB;AAA4B,YAAA,OAAO,EAAC,WAApC;AAAgD,YAAA,IAAI,EAAC,MAArD;AAA4D,YAAA,MAAM,EAAC,cAAnE;AAAkF,YAAA,WAAW,EAAC,KAA9F;AAAA,mCACE;AAAM,cAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AAVF;AAAA;AAAA;AAAA;AAAA,gBADF,eAeE;AAAI,UAAA,SAAS,EAAC,kBAAd;AAAiC,UAAA,KAAK,EAAE;AAAEA,YAAAA,KAAK,EAAE;AAAT,WAAxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAfF,eAgBE;AAAG,UAAA,SAAS,EAAC,iBAAb;AAA+B,UAAA,KAAK,EAAE;AAAER,YAAAA,QAAQ,EAAE;AAAZ,WAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAhBF;AAAA;AAAA;AAAA;AAAA;AAAA;AA/FJ;AAAA;AAAA;AAAA;AAAA,YAxCF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;;AAiKA,QAAMmC,eAAe,GAAG;AAAA;;AAAA,wBACtB;AACE,MAAA,SAAS,EAAC,mCADZ;AAEE,MAAA,KAAK,EAAE;AACLjC,QAAAA,UAAU,EAAE,mDADP;AAELC,QAAAA,YAAY,EAAE,MAFT;AAGLC,QAAAA,MAAM,EAAE,oCAHH;AAILC,QAAAA,SAAS,EAAE,uEAJN;AAKLC,QAAAA,cAAc,EAAE,YALX;AAMLR,QAAAA,MAAM,EAAE;AANH,OAFT;AAAA,8BAYE;AAAK,QAAA,SAAS,EAAC,mBAAf;AAAmC,QAAA,KAAK,EAAE;AAAES,UAAAA,WAAW,EAAE;AAAf,SAA1C;AAAA,+BACE;AAAK,UAAA,SAAS,EAAC,mDAAf;AAAA,kCACE;AAAA,oCACE;AAAI,cAAA,SAAS,EAAC,cAAd;AAA6B,cAAA,KAAK,EAAE;AAAEC,gBAAAA,KAAK,EAAE,SAAT;AAAoBR,gBAAAA,QAAQ,EAAE;AAA9B,eAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBADF,eAIE;AAAG,cAAA,SAAS,EAAC,iBAAb;AAA+B,cAAA,KAAK,EAAE;AAAEA,gBAAAA,QAAQ,EAAE;AAAZ,eAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eASE;AACE,YAAA,SAAS,EAAC,iDADZ;AAEE,YAAA,KAAK,EAAE;AACLE,cAAAA,UAAU,EAAE,2CADP;AAELE,cAAAA,MAAM,EAAE,MAFH;AAGLD,cAAAA,YAAY,EAAE,MAHT;AAILK,cAAAA,KAAK,EAAE,OAJF;AAKLR,cAAAA,QAAQ,EAAE,MALL;AAMLS,cAAAA,UAAU,EAAE;AANP,aAFT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBATF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cAZF,eAuCE;AAAK,QAAA,SAAS,EAAC,KAAf;AAAqB,QAAA,KAAK,EAAE;AAAEX,UAAAA,MAAM,EAAE,mBAAV;AAA+Bc,UAAAA,SAAS,EAAE;AAA1C,SAA5B;AAAA,kBACG,CAAC,0BAAA3C,iBAAiB,CAACmE,QAAlB,gFAA4BC,YAA5B,KAA4C,EAA7C,EAAiD1B,MAAjD,GAA0D,CAA1D,gBACC;AAAK,UAAA,SAAS,EAAC,0BAAf;AAAA,oBACG,CAAC,2BAAA1C,iBAAiB,CAACmE,QAAlB,kFAA4BC,YAA5B,KAA4C,EAA7C,EAAiDxB,KAAjD,CAAuD,CAAvD,EAA0D,CAA1D,EAA6DC,GAA7D,CAAiE,CAACwB,WAAD,EAActB,KAAd,KAAwB;AAAA;;AACxF,gBAAIuB,KAAK,GAAGrD,YAAY,CAACoD,WAAW,CAACC,KAAb,EAAoB,CAApB,CAAxB,CADwF,CAExF;;AACA,gBAAID,WAAW,CAACE,UAAZ,KAA2B,WAA/B,EAA4C;AAC1CD,cAAAA,KAAK,GAAGnB,IAAI,CAACqB,KAAL,CAAWF,KAAK,GAAG,CAAnB,CAAR;AACD;;AACD,kBAAMG,WAAW,GAAGxD,YAAY,CAACoD,WAAW,CAACK,YAAb,EAA2B,CAA3B,CAAhC;AACA,kBAAMC,QAAQ,GAAGxB,IAAI,CAACC,GAAL,CAAS,GAAG,CAAC,2BAAApD,iBAAiB,CAACmE,QAAlB,kFAA4BC,YAA5B,KAA4C,EAA7C,EAAiDvB,GAAjD,CAAqD+B,CAAC,IAAI3D,YAAY,CAAC2D,CAAC,CAACN,KAAH,EAAU,CAAV,CAAtE,CAAZ,EAAiG,CAAjG,CAAjB;AACA,kBAAMhB,eAAe,GAAGH,IAAI,CAACI,GAAL,CAAS,GAAT,EAAee,KAAK,GAAGK,QAAT,GAAqB,GAAnC,CAAxB;AACA,kBAAME,UAAU,GAAGzD,aAAa,CAACiD,WAAW,CAACE,UAAb,CAAhC;AAEA,gCACE;AAEE,cAAA,SAAS,EAAC,sCAFZ;AAGE,cAAA,KAAK,EAAE;AACLtC,gBAAAA,UAAU,EAAE,0BADP;AAELC,gBAAAA,YAAY,EAAE,MAFT;AAGLC,gBAAAA,MAAM,EAAE,oCAHH;AAILE,gBAAAA,cAAc,EAAE,WAJX;AAKLmB,gBAAAA,UAAU,EAAE,eALP;AAMLC,gBAAAA,MAAM,EAAE;AANH,eAHT;AAWE,cAAA,YAAY,EAAGC,CAAD,IAAO;AACnBA,gBAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAeC,SAAf,GAA2B,kBAA3B;AACAH,gBAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAexB,SAAf,GAA2B,oCAA3B;AACD,eAdH;AAeE,cAAA,YAAY,EAAGsB,CAAD,IAAO;AACnBA,gBAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAeC,SAAf,GAA2B,iBAA3B;AACAH,gBAAAA,CAAC,CAACC,MAAF,CAASC,KAAT,CAAexB,SAAf,GAA2B,gCAA3B;AACD,eAlBH;AAAA,sCAoBE;AAAK,gBAAA,SAAS,EAAC,wDAAf;AAAA,wCACE;AAAK,kBAAA,SAAS,EAAC,iCAAf;AAAA,0CACE;AACE,oBAAA,SAAS,EAAC,kDADZ;AAEE,oBAAA,KAAK,EAAE;AACLR,sBAAAA,KAAK,EAAE,MADF;AAELC,sBAAAA,MAAM,EAAE,MAFH;AAGLK,sBAAAA,YAAY,EAAE,MAHT;AAILD,sBAAAA,UAAU,EAAG,2BAA0B4C,UAAW,KAAIA,UAAW,KAJ5D;AAKLtC,sBAAAA,KAAK,EAAE;AALF,qBAFT;AAAA,2CAUE;AAAK,sBAAA,KAAK,EAAC,IAAX;AAAgB,sBAAA,MAAM,EAAC,IAAvB;AAA4B,sBAAA,OAAO,EAAC,WAApC;AAAgD,sBAAA,IAAI,EAAC,MAArD;AAA4D,sBAAA,MAAM,EAAC,cAAnE;AAAkF,sBAAA,WAAW,EAAC,GAA9F;AAAA,8CACE;AAAQ,wBAAA,EAAE,EAAC,IAAX;AAAgB,wBAAA,EAAE,EAAC,IAAnB;AAAwB,wBAAA,CAAC,EAAC;AAA1B;AAAA;AAAA;AAAA;AAAA,8BADF,eAEE;AAAM,wBAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA,8BAFF,eAGE;AAAM,wBAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA,8BAHF;AAAA;AAAA;AAAA;AAAA;AAAA;AAVF;AAAA;AAAA;AAAA;AAAA,0BADF,eAiBE;AAAA,4CACE;AAAG,sBAAA,SAAS,EAAC,kBAAb;AAAgC,sBAAA,KAAK,EAAE;AAAER,wBAAAA,QAAQ,EAAE,MAAZ;AAAoBQ,wBAAAA,KAAK,EAAE;AAA3B,uBAAvC;AAAA,gCACGhB,eAAe,CAAC8C,WAAW,CAACE,UAAb;AADlB;AAAA;AAAA;AAAA;AAAA,4BADF,eAIE;AAAG,sBAAA,SAAS,EAAC,iBAAb;AAA+B,sBAAA,KAAK,EAAE;AAAExC,wBAAAA,QAAQ,EAAE;AAAZ,uBAAtC;AAAA,iCACG0C,WADH,cACwBA,WAAW,KAAK,CAAhB,GAAoB,MAApB,GAA6B,OADrD;AAAA;AAAA;AAAA;AAAA;AAAA,4BAJF;AAAA;AAAA;AAAA;AAAA;AAAA,0BAjBF;AAAA;AAAA;AAAA;AAAA;AAAA,wBADF,eA2BE;AACE,kBAAA,SAAS,EAAC,wDADZ;AAEE,kBAAA,KAAK,EAAE;AACLxC,oBAAAA,UAAU,EAAG,2BAA0B4C,UAAW,KAAIA,UAAW,KAD5D;AAELtC,oBAAAA,KAAK,EAAE,OAFF;AAGLL,oBAAAA,YAAY,EAAE,MAHT;AAILH,oBAAAA,QAAQ,EAAE,MAJL;AAKLS,oBAAAA,UAAU,EAAE,KALP;AAMLwB,oBAAAA,QAAQ,EAAE,MANL;AAOLnC,oBAAAA,MAAM,EAAE;AAPH,mBAFT;AAAA,4BAYGpB,YAAY,CAAC6D,KAAD;AAZf;AAAA;AAAA;AAAA;AAAA,wBA3BF;AAAA;AAAA;AAAA;AAAA;AAAA,sBApBF,eAgEE;AACE,gBAAA,SAAS,EAAC,mBADZ;AAEE,gBAAA,KAAK,EAAE;AACLzC,kBAAAA,MAAM,EAAE,KADH;AAELI,kBAAAA,UAAU,EAAE,0BAFP;AAGLC,kBAAAA,YAAY,EAAE,KAHT;AAIL+B,kBAAAA,QAAQ,EAAE;AAJL,iBAFT;AAAA,uCASE;AACE,kBAAA,SAAS,EAAC,uCADZ;AAEE,kBAAA,KAAK,EAAE;AACLrC,oBAAAA,KAAK,EAAG,GAAE0B,eAAgB,GADrB;AAELrB,oBAAAA,UAAU,EAAG,0BAAyB4C,UAAW,KAAIA,UAAW,KAF3D;AAGL3C,oBAAAA,YAAY,EAAE,KAHT;AAILsB,oBAAAA,UAAU,EAAE;AAJP;AAFT;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA,sBAhEF;AAAA,eACOT,KADP;AAAA;AAAA;AAAA;AAAA,oBADF;AAsFD,WAjGA;AADH;AAAA;AAAA;AAAA;AAAA,gBADD,gBAsGC;AAAK,UAAA,SAAS,EAAC,gFAAf;AAAA,kCACE;AACE,YAAA,SAAS,EAAC,uDADZ;AAEE,YAAA,KAAK,EAAE;AACLnB,cAAAA,KAAK,EAAE,MADF;AAELC,cAAAA,MAAM,EAAE,MAFH;AAGLK,cAAAA,YAAY,EAAE,MAHT;AAILD,cAAAA,UAAU,EAAE,2CAJP;AAKLM,cAAAA,KAAK,EAAE;AALF,aAFT;AAAA,mCAUE;AAAK,cAAA,KAAK,EAAC,IAAX;AAAgB,cAAA,MAAM,EAAC,IAAvB;AAA4B,cAAA,OAAO,EAAC,WAApC;AAAgD,cAAA,IAAI,EAAC,MAArD;AAA4D,cAAA,MAAM,EAAC,cAAnE;AAAkF,cAAA,WAAW,EAAC,KAA9F;AAAA,qCACE;AAAM,gBAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AAVF;AAAA;AAAA;AAAA;AAAA,kBADF,eAeE;AAAI,YAAA,SAAS,EAAC,kBAAd;AAAiC,YAAA,KAAK,EAAE;AAAEA,cAAAA,KAAK,EAAE;AAAT,aAAxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAfF,eAgBE;AAAG,YAAA,SAAS,EAAC,iBAAb;AAA+B,YAAA,KAAK,EAAE;AAAER,cAAAA,QAAQ,EAAE;AAAZ,aAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAhBF;AAAA;AAAA;AAAA;AAAA;AAAA;AAvGJ;AAAA;AAAA;AAAA;AAAA,cAvCF;AAAA;AAAA;AAAA;AAAA;AAAA,YADsB;AAAA,GAAxB;;AAwKA,sBACE;AACE,IAAA,SAAS,EAAC,8BADZ;AAEE,IAAA,KAAK,EAAE;AACLE,MAAAA,UAAU,EAAE,aADP;AAEL6C,MAAAA,OAAO,EAAE;AAFJ,KAFT;AAAA,4BAQE;AACE,MAAA,SAAS,EAAC,4DADZ;AAEE,MAAA,KAAK,EAAE;AACL7C,QAAAA,UAAU,EAAE,2BADP;AAELC,QAAAA,YAAY,EAAE,MAFT;AAGLG,QAAAA,cAAc,EAAE,YAHX;AAILF,QAAAA,MAAM,EAAE,oCAJH;AAKLC,QAAAA,SAAS,EAAE;AALN,OAFT;AAAA,8BAUE;AAAA,gCACE;AAAI,UAAA,SAAS,EAAC,cAAd;AAA6B,UAAA,KAAK,EAAE;AAAEG,YAAAA,KAAK,EAAE,QAAT;AAAmBR,YAAAA,QAAQ,EAAE;AAA7B,WAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAIE;AAAG,UAAA,SAAS,EAAC,iBAAb;AAA+B,UAAA,KAAK,EAAE;AAAEA,YAAAA,QAAQ,EAAE;AAAZ,WAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,cAVF,eAkBE,QAAC,gBAAD;AAAA;AAAA;AAAA;AAAA,cAlBF;AAAA;AAAA;AAAA;AAAA;AAAA,YARF,eA8BE;AAAK,MAAA,SAAS,EAAC,SAAf;AAAA,8BACE;AAAK,QAAA,SAAS,EAAC,iBAAf;AAAA,+BACE,QAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cADF,eAIE;AAAK,QAAA,SAAS,EAAC,iBAAf;AAAA,+BACE,QAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cAJF;AAAA;AAAA;AAAA;AAAA;AAAA,YA9BF,EAwCG,CAAC7B,OAAO,IAAIF,iBAAiB,CAACE,OAA9B,kBACC;AACE,MAAA,SAAS,EAAC,2FADZ;AAEE,MAAA,KAAK,EAAE;AACL6E,QAAAA,MAAM,EAAE,IADH;AAEL9C,QAAAA,UAAU,EAAE,oBAFP;AAGLI,QAAAA,cAAc,EAAE;AAHX,OAFT;AAAA,6BAQE;AACE,QAAA,SAAS,EAAC,iBADZ;AAEE,QAAA,KAAK,EAAE;AACLJ,UAAAA,UAAU,EAAE,2BADP;AAELC,UAAAA,YAAY,EAAE,MAFT;AAGLG,UAAAA,cAAc,EAAE,YAHX;AAILF,UAAAA,MAAM,EAAE,oCAJH;AAKLC,UAAAA,SAAS,EAAE;AALN,SAFT;AAAA,gCAUE;AACE,UAAA,SAAS,EAAC,qBADZ;AAEE,UAAA,KAAK,EAAE;AACLR,YAAAA,KAAK,EAAE,MADF;AAELC,YAAAA,MAAM,EAAE,MAFH;AAGLU,YAAAA,KAAK,EAAE;AAHF,WAFT;AAOE,UAAA,IAAI,EAAC,QAPP;AAAA,iCASE;AAAM,YAAA,SAAS,EAAC,iBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA,gBAVF,eAqBE;AAAG,UAAA,SAAS,EAAC,gBAAb;AAA8B,UAAA,KAAK,EAAE;AAAEA,YAAAA,KAAK,EAAE;AAAT,WAArC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBArBF;AAAA;AAAA;AAAA;AAAA;AAAA;AARF;AAAA;AAAA;AAAA;AAAA,YAzCJ,eA8EE;AAAO,MAAA,GAAG,MAAV;AAAA,gBAAa;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlCM;AAAA;AAAA;AAAA;AAAA,YA9EF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;AAoHD,CA3gBD;;GAAMxC,sB;;KAAAA,sB;AA6gBN,eAAeA,sBAAf", "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ChatState } from '../../context/AllProviders';\r\n\r\nconst PageAnalyticsDashboard = () => {\r\n  const {\r\n    pageAnalyticsData,\r\n    onlineUsersData,\r\n    loading\r\n  } = ChatState();\r\n\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [selectedWebsite, setSelectedWebsite] = useState('all');\r\n  const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false);\r\n\r\n  // Utility functions\r\n  const formatNumber = (num) => {\r\n    const numValue = Number(num);\r\n    if (isNaN(numValue) || !isFinite(numValue)) return '0';\r\n    \r\n    if (numValue >= 1000000) return (numValue / 1000000).toFixed(1) + 'M';\r\n    if (numValue >= 1000) return (numValue / 1000).toFixed(1) + 'K';\r\n    return numValue.toString();\r\n  };\r\n\r\n  const safeGetValue = (value, fallback = 0) => {\r\n    const numValue = Number(value);\r\n    return isNaN(numValue) || !isFinite(numValue) ? fallback : numValue;\r\n  };\r\n\r\n  const getEventColor = (eventType) => {\r\n    const colors = {\r\n      'click': '#3B82F6',\r\n      'page_view': '#10B981', \r\n      'widget_open': '#8B5CF6',\r\n      'widget_close': '#8B5CF6',\r\n      'whatsapp_send_click': '#25D366',\r\n      'auto_widget_open': '#F59E0B',\r\n      'button_click': '#EF4444',\r\n      'link_click': '#06B6D4',\r\n      'scroll': '#84CC16',\r\n      'page_enter': '#10B981',\r\n      'page_exit': '#F97316'\r\n    };\r\n    return colors[eventType] || '#6B7280';\r\n  };\r\n\r\n  const formatEventName = (eventType) => {\r\n    const names = {\r\n      'whatsapp_send_click': 'WhatsApp Click',\r\n      'auto_widget_open': 'Auto Widget Open',\r\n      'widget_open': 'Widget Open', \r\n      'widget_close': 'Widget Close',\r\n      'button_click': 'Button Click',\r\n      'link_click': 'Link Click',\r\n      'page_view': 'Page View',\r\n      'page_enter': 'Page Enter',\r\n      'page_exit': 'Page Exit'\r\n    };\r\n    return names[eventType] || eventType.replace('_', ' ');\r\n  };\r\n\r\n  const ConnectionStatus = () => (\r\n    <div className=\"d-flex align-items-center gap-2\">\r\n      <div className=\"position-relative\">\r\n        <div \r\n          className={`rounded-circle ${onlineUsersData.isConnected ? 'bg-success' : 'bg-danger'}`} \r\n          style={{ width: '10px', height: '10px' }}\r\n        ></div>\r\n        {onlineUsersData.isConnected && (\r\n          <div \r\n            className=\"position-absolute top-0 start-0 rounded-circle bg-success animate-ping\" \r\n            style={{ width: '10px', height: '10px', opacity: '0.75' }}\r\n          ></div>\r\n        )}\r\n      </div>\r\n      <span className={`fw-medium ${onlineUsersData.isConnected ? 'text-success' : 'text-danger'}`} style={{ fontSize: '14px' }}>\r\n        {onlineUsersData.isConnected ? 'Live' : 'Offline'}\r\n      </span>\r\n    </div>\r\n  );\r\n\r\n  const TopPages = () => (\r\n    <div \r\n      className=\"position-relative overflow-hidden\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\r\n        borderRadius: '20px',\r\n        border: '1px solid rgba(226, 232, 240, 0.8)',\r\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n        backdropFilter: 'blur(10px)',\r\n        height: '480px'\r\n      }}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"p-4 border-bottom\" style={{ borderColor: 'rgba(226, 232, 240, 0.5)' }}>\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h6 className=\"mb-1 fw-bold\" style={{ color: '#1e293b', fontSize: '18px' }}>\r\n              Active Pages\r\n            </h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '13px' }}>\r\n              Real-time user activity\r\n            </p>\r\n          </div>\r\n          <div \r\n            className=\"badge d-flex align-items-center gap-1 px-3 py-2\"\r\n            style={{\r\n              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              color: 'white',\r\n              fontSize: '12px',\r\n              fontWeight: '600'\r\n            }}\r\n          >\r\n            <span>{(onlineUsersData.topPages || []).length}</span>\r\n            <span>pages</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"p-4\" style={{ height: 'calc(100% - 80px)', overflowY: 'auto' }}>\r\n        {(onlineUsersData.topPages || []).length > 0 ? (\r\n          <div className=\"d-flex flex-column gap-3\">\r\n            {(onlineUsersData.topPages || []).slice(0, 6).map((page, index) => {\r\n              const userCount = safeGetValue(page.users, 0);\r\n              const maxUsers = Math.max(...(onlineUsersData.topPages || []).map(p => safeGetValue(p.users, 0)), 1);\r\n              const widthPercentage = Math.min(100, (userCount / maxUsers) * 100);\r\n              \r\n              return (\r\n                <div \r\n                  key={index} \r\n                  className=\"position-relative p-3 transition-all\"\r\n                  style={{\r\n                    background: 'rgba(255, 255, 255, 0.7)',\r\n                    borderRadius: '16px',\r\n                    border: '1px solid rgba(226, 232, 240, 0.6)',\r\n                    backdropFilter: 'blur(8px)',\r\n                    transition: 'all 0.3s ease',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.transform = 'translateY(-2px)';\r\n                    e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.transform = 'translateY(0px)';\r\n                    e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                >\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div className=\"d-flex align-items-center gap-3\">\r\n                      <div \r\n                        className=\"d-flex align-items-center justify-content-center\"\r\n                        style={{\r\n                          width: '32px',\r\n                          height: '32px',\r\n                          borderRadius: '10px',\r\n                          background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\r\n                          color: 'white',\r\n                          fontSize: '14px',\r\n                          fontWeight: '600'\r\n                        }}\r\n                      >\r\n                        {index + 1}\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"mb-0 fw-semibold\" style={{ fontSize: '14px', color: '#1e293b' }}>\r\n                          {page.path === '/' ? 'Homepage' : (page.path?.length > 25 ? page.path.substring(0, 25) + '...' : page.path) || 'Unknown'}\r\n                        </p>\r\n                        <p className=\"mb-0 text-muted\" style={{ fontSize: '12px' }}>\r\n                          {userCount} active {userCount === 1 ? 'user' : 'users'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div \r\n                      className=\"badge d-flex align-items-center justify-content-center\"\r\n                      style={{\r\n                        background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\r\n                        color: 'white',\r\n                        borderRadius: '12px',\r\n                        fontSize: '12px',\r\n                        fontWeight: '600',\r\n                        minWidth: '40px',\r\n                        height: '28px'\r\n                      }}\r\n                    >\r\n                      {userCount}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Modern Progress Bar */}\r\n                  <div \r\n                    className=\"position-relative\"\r\n                    style={{\r\n                      height: '6px',\r\n                      background: 'rgba(226, 232, 240, 0.5)',\r\n                      borderRadius: '6px',\r\n                      overflow: 'hidden'\r\n                    }}\r\n                  >\r\n                    <div \r\n                      className=\"position-absolute top-0 start-0 h-100\"\r\n                      style={{\r\n                        width: `${widthPercentage}%`,\r\n                        background: `linear-gradient(90deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}cc)`,\r\n                        borderRadius: '6px',\r\n                        transition: 'width 0.5s ease'\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        ) : (\r\n          <div className=\"d-flex flex-column align-items-center justify-content-center h-100 text-center\">\r\n            <div \r\n              className=\"mb-3 d-flex align-items-center justify-content-center\"\r\n              style={{\r\n                width: '80px',\r\n                height: '80px',\r\n                borderRadius: '20px',\r\n                background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\r\n                color: '#64748b'\r\n              }}\r\n            >\r\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1.5\">\r\n                <path d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\r\n              </svg>\r\n            </div>\r\n            <h6 className=\"mb-2 fw-semibold\" style={{ color: '#475569' }}>No Active Pages</h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '14px' }}>\r\n              Page activity will appear here when users visit\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const TopInteractions = () => (\r\n    <div \r\n      className=\"position-relative overflow-hidden\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\r\n        borderRadius: '20px',\r\n        border: '1px solid rgba(226, 232, 240, 0.8)',\r\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n        backdropFilter: 'blur(10px)',\r\n        height: '480px'\r\n      }}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"p-4 border-bottom\" style={{ borderColor: 'rgba(226, 232, 240, 0.5)' }}>\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h6 className=\"mb-1 fw-bold\" style={{ color: '#1e293b', fontSize: '18px' }}>\r\n              User Interactions\r\n            </h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '13px' }}>\r\n              Today's activity breakdown\r\n            </p>\r\n          </div>\r\n          <div \r\n            className=\"badge d-flex align-items-center gap-1 px-3 py-2\"\r\n            style={{\r\n              background: 'linear-gradient(135deg, #10b981, #059669)',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              color: 'white',\r\n              fontSize: '12px',\r\n              fontWeight: '600'\r\n            }}\r\n          >\r\n            Today\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"p-4\" style={{ height: 'calc(100% - 80px)', overflowY: 'auto' }}>\r\n        {(pageAnalyticsData.behavior?.interactions || []).length > 0 ? (\r\n          <div className=\"d-flex flex-column gap-3\">\r\n            {(pageAnalyticsData.behavior?.interactions || []).slice(0, 7).map((interaction, index) => {\r\n              let count = safeGetValue(interaction.count, 0);\r\n              // Divide page_exit count by 2 to fix double counting issue\r\n              if (interaction.event_type === 'page_exit') {\r\n                count = Math.round(count / 2);\r\n              }\r\n              const uniqueUsers = safeGetValue(interaction.unique_users, 0);\r\n              const maxCount = Math.max(...(pageAnalyticsData.behavior?.interactions || []).map(i => safeGetValue(i.count, 0)), 1);\r\n              const widthPercentage = Math.min(100, (count / maxCount) * 100);\r\n              const eventColor = getEventColor(interaction.event_type);\r\n              \r\n              return (\r\n                <div \r\n                  key={index} \r\n                  className=\"position-relative p-3 transition-all\"\r\n                  style={{\r\n                    background: 'rgba(255, 255, 255, 0.7)',\r\n                    borderRadius: '16px',\r\n                    border: '1px solid rgba(226, 232, 240, 0.6)',\r\n                    backdropFilter: 'blur(8px)',\r\n                    transition: 'all 0.3s ease',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.transform = 'translateY(-2px)';\r\n                    e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.transform = 'translateY(0px)';\r\n                    e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                >\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div className=\"d-flex align-items-center gap-3\">\r\n                      <div \r\n                        className=\"d-flex align-items-center justify-content-center\"\r\n                        style={{\r\n                          width: '36px',\r\n                          height: '36px',\r\n                          borderRadius: '12px',\r\n                          background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\r\n                          color: 'white'\r\n                        }}\r\n                      >\r\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                          <circle cx=\"12\" cy=\"12\" r=\"3\"/>\r\n                          <path d=\"m12 1 0 6m0 6 0 6\"/>\r\n                          <path d=\"m17 12-6 0m-6 0 6 0\"/>\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"mb-0 fw-semibold\" style={{ fontSize: '14px', color: '#1e293b' }}>\r\n                          {formatEventName(interaction.event_type)}\r\n                        </p>\r\n                        <p className=\"mb-0 text-muted\" style={{ fontSize: '12px' }}>\r\n                          {uniqueUsers} unique {uniqueUsers === 1 ? 'user' : 'users'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div \r\n                      className=\"badge d-flex align-items-center justify-content-center\"\r\n                      style={{\r\n                        background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\r\n                        color: 'white',\r\n                        borderRadius: '12px',\r\n                        fontSize: '12px',\r\n                        fontWeight: '600',\r\n                        minWidth: '45px',\r\n                        height: '28px'\r\n                      }}\r\n                    >\r\n                      {formatNumber(count)}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Modern Progress Bar */}\r\n                  <div \r\n                    className=\"position-relative\"\r\n                    style={{\r\n                      height: '6px',\r\n                      background: 'rgba(226, 232, 240, 0.5)',\r\n                      borderRadius: '6px',\r\n                      overflow: 'hidden'\r\n                    }}\r\n                  >\r\n                    <div \r\n                      className=\"position-absolute top-0 start-0 h-100\"\r\n                      style={{\r\n                        width: `${widthPercentage}%`,\r\n                        background: `linear-gradient(90deg, ${eventColor}, ${eventColor}cc)`,\r\n                        borderRadius: '6px',\r\n                        transition: 'width 0.5s ease'\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        ) : (\r\n          <div className=\"d-flex flex-column align-items-center justify-content-center h-100 text-center\">\r\n            <div \r\n              className=\"mb-3 d-flex align-items-center justify-content-center\"\r\n              style={{\r\n                width: '80px',\r\n                height: '80px',\r\n                borderRadius: '20px',\r\n                background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\r\n                color: '#64748b'\r\n              }}\r\n            >\r\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1.5\">\r\n                <path d=\"M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122\"/>\r\n              </svg>\r\n            </div>\r\n            <h6 className=\"mb-2 fw-semibold\" style={{ color: '#475569' }}>No Interactions Yet</h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '14px' }}>\r\n              User interactions will be displayed here\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div \r\n      className=\"min-vh-100 position-relative\"\r\n      style={{\r\n        background: 'transparent',\r\n        padding: '2rem'\r\n      }}\r\n    >\r\n      {/* Modern Header */}\r\n      <div \r\n        className=\"d-flex justify-content-between align-items-center mb-4 p-4\"\r\n        style={{\r\n          background: 'rgba(255, 255, 255, 0.95)',\r\n          borderRadius: '20px',\r\n          backdropFilter: 'blur(20px)',\r\n          border: '1px solid rgba(255, 255, 255, 0.2)',\r\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\r\n        }}\r\n      >\r\n        <div>\r\n          <h4 className=\"mb-1 fw-bold\" style={{ color: 'maroon', fontSize: '24px' }}>\r\n            Widget Analytics\r\n          </h4>\r\n          <p className=\"mb-0 text-muted\" style={{ fontSize: '14px' }}>\r\n            Real-time insights and user behavior\r\n          </p>\r\n        </div>\r\n        <ConnectionStatus />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"row g-4\">\r\n        <div className=\"col-12 col-xl-6\">\r\n          <TopPages />\r\n        </div>\r\n        <div className=\"col-12 col-xl-6\">\r\n          <TopInteractions />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modern Loading Indicator */}\r\n      {(loading || pageAnalyticsData.loading) && (\r\n        <div \r\n          className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\r\n          style={{ \r\n            zIndex: 9999,\r\n            background: 'rgba(0, 0, 0, 0.4)',\r\n            backdropFilter: 'blur(8px)'\r\n          }}\r\n        >\r\n          <div \r\n            className=\"text-center p-4\"\r\n            style={{\r\n              background: 'rgba(255, 255, 255, 0.95)',\r\n              borderRadius: '20px',\r\n              backdropFilter: 'blur(20px)',\r\n              border: '1px solid rgba(255, 255, 255, 0.2)',\r\n              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            <div \r\n              className=\"spinner-border mb-3\"\r\n              style={{ \r\n                width: '3rem', \r\n                height: '3rem',\r\n                color: '#667eea'\r\n              }}\r\n              role=\"status\"\r\n            >\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n            <p className=\"mb-0 fw-medium\" style={{ color: '#1e293b' }}>\r\n              Updating Analytics...\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Custom CSS for animations */}\r\n      <style jsx>{`\r\n        .animate-ping {\r\n          animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\r\n        }\r\n        \r\n        @keyframes ping {\r\n          75%, 100% {\r\n            transform: scale(2);\r\n            opacity: 0;\r\n          }\r\n        }\r\n        \r\n        .transition-all {\r\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n        }\r\n        \r\n        /* Custom scrollbar */\r\n        ::-webkit-scrollbar {\r\n          width: 6px;\r\n        }\r\n        \r\n        ::-webkit-scrollbar-track {\r\n          background: rgba(226, 232, 240, 0.3);\r\n          border-radius: 6px;\r\n        }\r\n        \r\n        ::-webkit-scrollbar-thumb {\r\n          background: rgba(148, 163, 184, 0.5);\r\n          border-radius: 6px;\r\n        }\r\n        \r\n        ::-webkit-scrollbar-thumb:hover {\r\n          background: rgba(148, 163, 184, 0.7);\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageAnalyticsDashboard;\r\n"]}, "metadata": {}, "sourceType": "module"}