import React, { createContext, useContext, useEffect, useState } from "react";
import { getCookie } from "../utils/Utils";
import { io } from "socket.io-client";
import { SOCKET_URL, BASE_URL, BASE_URL2 } from "../api/api";
import axios from "axios";
import { AuthContext } from "./AuthContext";
import { ChatContext } from "./ChatContext";
import { deleteCookie } from "../utils/Utils";

const AllContext = createContext();

const AllProvider = ({ children }) => {
  // const navigate= useNavigate();
  const [loading, setLoading] = useState(false);
  const { currentUser } = useContext(AuthContext);
  const { dispatch } = useContext(ChatContext);
  const [text, setText] = useState("");
  const [reply, setReply] = useState(null);
  const [socket, setSocket] = useState(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [unReadChat, setUnReadChat] = useState([]);
  const [selectedMobileNumber, setSelectedMobileNumber] = useState(null);
  const [selectedUserDetails, setSelectedUserDetails] = useState({
    email: "",
    company: ""
  })
  const [chats, setChats] = useState([]);
  const [convpage, setConvPage] = useState(0);
  const [scrolarinmiddle, setScrolarinmiddle] = useState(false);
  const [page, setPage] = useState(0);
  const [allChats, setAllChats] = useState([]);
  const [starChats, setStarChats] = useState([]);
  const [channel, setChannel] = useState("");
  const [chatCategory, setChatCategory] = useState("all");
  const [chatFilterType, setChatFilterType] = useState({ value: "All Agents", label: "All Agents" });
  const [roleFilterType, setRoleFilterType] = useState("");
  const [nextConvId, setNextConvId] = useState(null);
  const [selectedChannel,setSelectedChannel]=useState('');
  const [unreadCount,setUnreadCount]=useState(0);
  const [waitingChats, setWaitingChats] = useState([]);
  const [filteredChats, setFilteredChats] = useState([]);
  const [checkboxList, setCheckboxList] = useState([]);
  const [chatsLoading, setChatsLoading] = useState(false);
  const [selectedName, setSelectedName] = useState("");
  const [isOldMsg, setIsOldMsg] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const [sendTemplatePopUp, setSendTemplatePopUp] = useState(false);
  const [wpProfile, setWpProfile] = useState([]);
  const [callData, setCallData] = useState({
    visible: false,
    name: "",
    mobile: null,
    content: "",
    message_type: "",
  });
  const [mobileVisible, setMobileVisible] = useState(false);
  const [breakInfo, setBreakInfo] = useState(null);
  const [pipelines, setPipelines] = useState([
    {
      id: '1',
      name: 'Leads',
      stages: [
        { id: '1-1', name: 'Contacted' },
        { id: '1-2', name: 'Qualified' },
      ],
    },
    {
      id: '2',
      name: 'Sales Funnel',
      stages: [
        { id: '2-1', name: 'Demo Scheduled' },
        { id: '2-2', name: 'Proposal Sent' },
        { id: '2-3', name: 'Negotiation' },
      ],
    },
    {
      id: '3',
      name: 'Partnerships',
      stages: [
        { id: '3-1', name: 'Outreach' },
        { id: '3-2', name: 'Call Booked' },
      ],
    },
  ]);

  const [activePipelineId, setActivePipelineId] = useState('');
  const [agents, setAgents] = useState([]);

  // ✅ ENHANCED: Online Users WebSocket state with analytics
  const [onlineUsersSocket, setOnlineUsersSocket] = useState(null);
  const [onlineUsersData, setOnlineUsersData] = useState({
    totalOnline: 0,
    usersByCountry: [],
    usersByCity: [],
    lastUpdated: null,
    isConnected: false,
    dataType: 'real-time',
    // ✅ NEW: Enhanced analytics data
    totalUsers: 0,
    totalSessions: 0,
    activeUsers: 0,
    idleUsers: 0,
    topPages: []
  });

  // ✅ NEW: Page Analytics specific state
  const [pageAnalyticsData, setPageAnalyticsData] = useState({
    overview: {
      realTime: {
        totalUsers: 0,
        totalSessions: 0,
        activeUsers: 0,
        idleUsers: 0,
        topPages: [],
        usersByCountry: [],
        usersByCity: []
      },
      today: {
        unique_users: 0,
        total_events: 0,
        unique_sessions: 0,
        avg_time_on_page: 0,
        avg_scroll_depth: 0
      },
      topEvents: []
    },
    pages: [],
    behavior: {
      sessions: [],
      interactions: []
    },
    loading: false,
    lastUpdated: null
  });

  // ✅ NEW: Analytics filters and settings
  const [analyticsFilters, setAnalyticsFilters] = useState({
    selectedDate: null,
    selectedDateRange: 'today',
    selectedPage: 'all',
    limit: 50
  });

  // Historical data states
  const [historicalMode, setHistoricalMode] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [availableDates, setAvailableDates] = useState([]);
  const [historicalData, setHistoricalData] = useState({
    totalOnline: 0,
    usersByCountry: [],
    usersByCity: [],
    lastUpdated: null,
    dataType: 'historical',
    date: null
  });

  useEffect(() => {
    const userCookie = getCookie("user");
    const userInfo = userCookie ? JSON.parse(userCookie) : null;
    const currentuser = userInfo?.data;
    const socketconn = io(SOCKET_URL);
    if (currentuser?.parent_id) {
      // console.log(currentuser);
      currentuser.item = "test"
      socketconn.emit("setup", currentuser);

      socketconn.on('user_logout', (data) => {
        if (currentUser.user_id === data.user_id && currentUser.user_type !== "admin") {
          deleteCookie("user");
        }
      })

      setSocket(socketconn);
      socketconn.on('user_not_found', (data) => {
        if (currentUser.user_id === data.user_id && currentUser.user_type !== "admin") {
          deleteCookie("user");
        }
      })
    }
    return () => {
      socketconn.off('user_not_found');
      socketconn.off('user_logout');
      socketconn.disconnect();
    };
  }, [currentUser]);

  // ✅ ENHANCED: Real-time WebSocket connection with comprehensive analytics
  useEffect(() => {
    if (!currentUser || !currentUser.parent_id) return;
    const wsUrl = 'ws://localhost:8080';
    let ws = null;
    let reconnectInterval = null;

    const connectWebSocket = () => {
      try {
        console.log('🔌 Connecting to Enhanced Analytics WebSocket...');
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log('🔌 ✅ Enhanced Analytics WebSocket connected');
          setOnlineUsersData(prev => ({ ...prev, isConnected: true }));
          
          // Clear any existing reconnection attempts
          if (reconnectInterval) {
            clearInterval(reconnectInterval);
            reconnectInterval = null;
          }

          // Send dashboard connect message to receive real-time data
          const connectMessage = {
            type: 'dashboard_connect',
            timestamp: Date.now()
          };
          console.log('📤 Sending dashboard connect message:', connectMessage);
          ws.send(JSON.stringify(connectMessage));
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('📨 [WEBSOCKET] Received message type:', data.type);

            if (data.type === 'dashboard_data') {
              console.log('📊 [WEBSOCKET] Comprehensive dashboard data received:', {
                totalUsers: data.totalUsers,
                totalSessions: data.totalSessions,
                todayStatsExists: !!data.todayStats,
                pageStatsCount: data.pageStats?.length || 0,
                behaviorStatsExists: !!data.behaviorStats,
                topEventsCount: data.topEvents?.length || 0
              });
              
              // ✅ Update online users data (for existing functionality)
              setOnlineUsersData(prev => ({
                ...prev,
                totalOnline: data.totalUsers || data.totalOnline || 0,
                usersByCountry: Array.isArray(data.usersByCountry) ? data.usersByCountry : [],
                usersByCity: Array.isArray(data.usersByCity) ? data.usersByCity : [],
                lastUpdated: data.timestamp || Date.now(),
                dataType: data.dataType || 'real-time-analytics',
                // Enhanced analytics fields
                totalUsers: data.totalUsers || 0,
                totalSessions: data.totalSessions || 0,
                activeUsers: data.activeUsers || 0,
                idleUsers: data.idleUsers || 0,
                topPages: Array.isArray(data.topPages) ? data.topPages : []
              }));

              // ✅ Update page analytics with ALL comprehensive data from WebSocket
              setPageAnalyticsData(prev => ({
                ...prev,
                overview: {
                  realTime: {
                    totalUsers: data.totalUsers || 0,
                    totalSessions: data.totalSessions || 0,
                    activeUsers: data.activeUsers || 0,
                    idleUsers: data.idleUsers || 0,
                    topPages: Array.isArray(data.topPages) ? data.topPages : [],
                    usersByCountry: Array.isArray(data.usersByCountry) ? data.usersByCountry : [],
                    usersByCity: Array.isArray(data.usersByCity) ? data.usersByCity : []
                  },
                  // ✅ CRITICAL: Real-time today's stats from WebSocket
                  today: data.todayStats || {
                    unique_users: 0,
                    total_events: 0,
                    unique_sessions: 0,
                    avg_time_on_page: 0,
                    avg_scroll_depth: 0
                  },
                  // ✅ CRITICAL: Real-time top events from WebSocket
                  topEvents: Array.isArray(data.topEvents) ? data.topEvents : []
                },
                // ✅ CRITICAL: Real-time page performance from WebSocket
                pages: Array.isArray(data.pageStats) ? data.pageStats : [],
                // ✅ CRITICAL: Real-time user behavior from WebSocket
                behavior: data.behaviorStats || { sessions: [], interactions: [] },
                loading: false,
                lastUpdated: data.timestamp || Date.now()
              }));

              console.log('✅ [WEBSOCKET] All analytics data updated successfully');
            }

            // Handle other message types
            if (data.type === 'live_users_count') {
              console.log('👥 [WEBSOCKET] Live users count update:', data.count);
              setOnlineUsersData(prev => ({
                ...prev,
                totalOnline: data.count,
                totalUsers: data.count,
                lastUpdated: Date.now()
              }));
            }

          } catch (error) {
            console.error('❌ [WEBSOCKET] Error parsing message:', error);
            console.error('❌ [WEBSOCKET] Raw message:', event.data);
          }
        };

        ws.onclose = (event) => {
          console.log('🔌 ❌ Analytics WebSocket disconnected:', event.code, event.reason);
          setOnlineUsersData(prev => ({ 
            ...prev, 
            isConnected: false 
          }));

          // Attempt to reconnect after 3 seconds
          if (!reconnectInterval) {
            console.log('🔄 Setting up reconnection...');
            reconnectInterval = setTimeout(() => {
              console.log('🔄 Attempting to reconnect...');
              connectWebSocket();
            }, 3000);
          }
        };

        ws.onerror = (error) => {
          console.error('❌ [WEBSOCKET] Analytics WebSocket error:', error);
          setOnlineUsersData(prev => ({ 
            ...prev, 
            isConnected: false 
          }));
        };

        setOnlineUsersSocket(ws);

      } catch (error) {
        console.error('❌ [WEBSOCKET] Failed to connect:', error);
      }
    };

    // Start the connection
    connectWebSocket();

    return () => {
      if (reconnectInterval) {
        clearTimeout(reconnectInterval);
      }
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [currentUser]);

  useEffect(() => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id || currentUser.user_type !== "agent") return;
    const fetchAgentBreak = async () => {
      const payload = {
        user_id: currentUser.user_id,
        token: currentUser.token,
        method: "retrieve_agent_break",
        user_type: currentUser.user_type
      }
      try {
        const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, payload)
        if (data.success && data.data) {

          const breakInfo = {
            active: data.data.break_status === 1 ? true : false,
            breakUntil: data.data.break_until,
            mode: data.data.break_mode,
            id: data.data._id,
          }
          setBreakInfo(breakInfo);

        }

      } catch (error) {
        console.log(error);

      }
    }

    fetchAgentBreak();

  }, [currentUser])

  // ✅ REPLACED: Simple initial load (no more API polling!)
  useEffect(() => {
    if (!currentUser || !currentUser.parent_id) return;
    
    // Only load historical dates once
    fetchAvailableDates();
  }, [currentUser]);

  const handleChatOpen = async (item) => {
    setSelectedMobileNumber(item.mobile);
    setSelectedName(item.name);
    setLoading(true);
    setConvPage(0);
    setText("");

    let readdata = {
      ...item,
      brand_number: currentUser.brand_number,
      parent_id: currentUser.parent_id,
    };

    const givenDate = new Date(item.created);
    const currentDate = new Date();
    const time_23hrs_59min_ago = new Date(
      currentDate.getTime() - (23 * 60 * 60 * 1000 + 59 * 60 * 1000)
    );
    if (givenDate < time_23hrs_59min_ago) {
      setIsOldMsg(true);
    } else {
      setIsOldMsg(false);
    }
    try {
      const forconvdata = {
        token: currentUser.parent_token,
        user_id: currentUser.parent_id,
        method: "conv_list_new",
        brand_number: currentUser.brand_number,
        start: 0,
        from_mobile: item.mobile,
      };

      const res = await axios.post(
        `${BASE_URL}/netcore_conversation.php`,
        forconvdata
      );

      if (res.data.success === true) {
        let updatedunreadchat = unReadChat.filter(
          (items, index) => items.read_status === 0
        );

        setUnReadChat(updatedunreadchat);

        const index = chats.findIndex(
          (selecteditem) => selecteditem.mobile === readdata.mobile
        );

        const unreadindex = updatedunreadchat.findIndex(
          (selecteditem) => selecteditem.mobile === readdata.mobile
        );

        if (unreadindex > -1) {
          const updatedUnreadItems = [...updatedunreadchat];
          updatedUnreadItems[index] = {
            ...updatedUnreadItems[index],

            read_status: 1,
          };

          updatedUnreadItems.sort(
            (a, b) => a.id - b.id
          );

          setUnReadChat(updatedUnreadItems);
        }

        if (index !== -1) {
          const updatedItems = [...chats];
          updatedItems[index] = {
            ...updatedItems[index],

            read_status: 1,
          };

          updatedItems.sort(
            (a, b) => new Date(b.created) - new Date(a.created)
          );

          setChats(updatedItems);
        }

        if (chatCategory === "all") {
          setUnReadChat((prevItems) =>
            prevItems.filter((items, index) => items.mobile !== readdata.mobile)
          );
        }

        let updatedconv = res.data.data;
        updatedconv.sort((a, b) => a.id - b.id);

        await dispatch({
          type: "CHANGE_USER",
          payload: {
            mobile: item.mobile,
            conversation: updatedconv,
            name: item.name,
          },
        });
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const startBreak = ({ type, breakUntil, id }) => {
    setBreakInfo({
      active: true,
      type,
      breakUntil,
      id
    });
  };

  const endBreak = () => {
    setBreakInfo(null);
  };

  const fetchAgents = async () => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
    const payload = {
      user_id: currentUser.parent_id,
      method: "retrieve_agent",
      token: currentUser.parent_token,
      user_type: currentUser.user_type,
      agent_id: currentUser.user_id,
    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);

      if (data.success) {
        const agents = data.data.filter((item) => item.agent_type === "agent")
        const formattedAgentList = agents.map((item) => ({
          value: item.id,
          label: item.name
        }))

        setAgents([...formattedAgentList]);
      }

    } catch (error) {
      console.error("Error fetching agents:", error);
    }
  }

  // ✅ NEW: Manual refresh function (uses WebSocket instead of API)
  const handleManualRefresh = () => {
    console.log('🔄 [MANUAL] Manual refresh requested');
    setPageAnalyticsData(prev => ({ ...prev, loading: true }));
    
    // Force WebSocket to send fresh data
    if (onlineUsersSocket && onlineUsersSocket.readyState === WebSocket.OPEN) {
      const refreshMessage = {
        type: 'refresh_request',
        timestamp: Date.now()
      };
      console.log('📤 [MANUAL] Sending refresh request:', refreshMessage);
      onlineUsersSocket.send(JSON.stringify(refreshMessage));
      
      // Reset loading after a short delay
      setTimeout(() => {
        setPageAnalyticsData(prev => ({ ...prev, loading: false }));
      }, 1000);
    } else {
      console.log('❌ [MANUAL] WebSocket not connected for refresh');
      setPageAnalyticsData(prev => ({ ...prev, loading: false }));
    }
  };

  // ✅ KEEP: Historical data API functions (only for historical dates)
  const fetchAnalyticsOverview = async () => {
    if (!currentUser || !currentUser.parent_id) return;
    
    try {
      setPageAnalyticsData(prev => ({ ...prev, loading: true }));
      
      const response = await axios.get("http://localhost:3001/api/analytics/overview");
      
      if (response.data.success) {
        setPageAnalyticsData(prev => ({
          ...prev,
          overview: response.data.data,
          loading: false,
          lastUpdated: Date.now()
        }));
        console.log('✅ [API] Analytics overview loaded:', response.data.data);
      }
    } catch (error) {
      console.error('❌ [API] Error fetching analytics overview:', error);
      setPageAnalyticsData(prev => ({ ...prev, loading: false }));
    }
  };

  const fetchPageAnalytics = async (date = null, limit = 50) => {
    if (!currentUser || !currentUser.parent_id) return;
    
    try {
      setPageAnalyticsData(prev => ({ ...prev, loading: true }));
      
      const params = new URLSearchParams();
      if (date) params.append('date', date);
      params.append('limit', limit.toString());
      
      const response = await axios.get(`http://localhost:3001/api/analytics/pages?${params}`);
      
      if (response.data.success) {
        setPageAnalyticsData(prev => ({
          ...prev,
          pages: response.data.data,
          loading: false,
          lastUpdated: Date.now()
        }));
        console.log('✅ [API] Page analytics loaded:', response.data.data);
      }
    } catch (error) {
      console.error('❌ [API] Error fetching page analytics:', error);
      setPageAnalyticsData(prev => ({ ...prev, loading: false }));
    }
  };

  const fetchBehaviorAnalytics = async (date = null) => {
    if (!currentUser || !currentUser.parent_id) return;
    
    try {
      setPageAnalyticsData(prev => ({ ...prev, loading: true }));
      
      const params = new URLSearchParams();
      if (date) params.append('date', date);
      
      const response = await axios.get(`http://localhost:3001/api/analytics/behavior?${params}`);
      
      if (response.data.success) {
        setPageAnalyticsData(prev => ({
          ...prev,
          behavior: response.data.data,
          loading: false,
          lastUpdated: Date.now()
        }));
        console.log('✅ [API] Behavior analytics loaded:', response.data.data);
      }
    } catch (error) {
      console.error('❌ [API] Error fetching behavior analytics:', error);
      setPageAnalyticsData(prev => ({ ...prev, loading: false }));
    }
  };

  const loadAllAnalytics = async (date = null) => {
    await Promise.all([
      fetchAnalyticsOverview(),
      fetchPageAnalytics(date),
      fetchBehaviorAnalytics(date)
    ]);
  };

  // ✅ UPDATED: Update analytics filters (only use API for historical dates)
  const updateAnalyticsFilters = (newFilters) => {
    setAnalyticsFilters(prev => ({ ...prev, ...newFilters }));
    
    // Only refetch data for historical dates (real-time is always current)
    if (newFilters.selectedDate) {
      console.log('📅 Loading historical analytics for:', newFilters.selectedDate);
      loadAllAnalytics(newFilters.selectedDate);
    } else {
      console.log('🔄 Refreshing real-time analytics');
      handleManualRefresh();
    }
  };

  // Historical data functions
  const fetchAvailableDates = async () => {
    if (!currentUser || !currentUser.parent_id) return;
    
    try {
      console.log('🔍 Fetching available dates from: http://localhost:3001/api/available-dates');
      const response = await axios.get("http://localhost:3001/api/available-dates");
      console.log('📅 Raw server response:', response.data);
      
      if (response.data.success) {
        response.data.dates.forEach((dateInfo, index) => {
          console.log(`📅 Date ${index}:`, {
            serverDate: dateInfo.date,
            userCount: dateInfo.user_count,
            jsDate: new Date(dateInfo.date),
            formatted: new Date(dateInfo.date).toLocaleDateString()
          });
        });
        
        setAvailableDates(response.data.dates);
        console.log(`📅 Found ${response.data.dates.length} dates with data`);
      } else {
        console.error('❌ API returned success: false', response.data);
      }
    } catch (error) {
      console.error('❌ Error fetching available dates:', error);
      console.error('❌ Error response:', error.response?.data);
    }
  };

  const fetchHistoricalData = async (date) => {
    if (!currentUser || !currentUser.parent_id || !date) return;
    
    try {
      setLoading(true);
      console.log(`📊 Fetching historical data for ${date}`);
      
      let dateOnly;
      if (typeof date === 'string' && date.includes('T')) {
        dateOnly = date.split('T')[0];
      } else if (date instanceof Date) {
        dateOnly = date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0');
      } else {
        dateOnly = date;
      }
      
      console.log(`📅 Sending date to server: ${dateOnly}`);
      
      const response = await axios.post("http://localhost:3001/api/historical-dashboard", { 
        date: dateOnly
      });
      
      if (response.data.success) {
        setHistoricalData({
          ...response.data.data,
          date: dateOnly
        });
        console.log(`✅ Historical data loaded for ${dateOnly}`);
      }
    } catch (error) {
      console.error('Error fetching historical data:', error);
    } finally {
      setLoading(false);
    }
  };

  const switchToHistoricalMode = (date) => {
    console.log(`🔄 Switching to historical mode for ${date}`);
    setHistoricalMode(true);
    setSelectedDate(date);
    fetchHistoricalData(date);
  };

  const switchToRealTimeMode = () => {
    console.log('🔄 Switching to real-time mode');
    setHistoricalMode(false);
    setSelectedDate(null);
    setHistoricalData({
      totalOnline: 0,
      usersByCountry: [],
      usersByCity: [],
      lastUpdated: null,
      dataType: 'historical',
      date: null
    });
  };

  // Helper function to get top countries by user count
  const getTopCountries = (limit = 10) => {
    const data = historicalMode ? historicalData : onlineUsersData;
    return data.usersByCountry.slice(0, limit);
  };

  // Helper function to get top cities by user count
  const getTopCities = (limit = 10) => {
    const data = historicalMode ? historicalData : onlineUsersData;
    return data.usersByCity.slice(0, limit);
  };

  // Helper function to get users by specific country
  const getUsersByCountry = (countryName) => {
    const data = historicalMode ? historicalData : onlineUsersData;
    const country = data.usersByCountry.find(
      c => c.country.toLowerCase() === countryName.toLowerCase()
    );
    return country ? country.user_count : 0;
  };

  // Helper function to get users by specific city
  const getUsersByCity = (cityName) => {
    const data = historicalMode ? historicalData : onlineUsersData;
    const city = data.usersByCity.find(
      c => c.city.toLowerCase() === cityName.toLowerCase()
    );
    return city ? city.user_count : 0;
  };

  // ✅ NEW: Helper functions for page analytics
  const getTopPerformingPages = (limit = 10) => {
    return pageAnalyticsData.pages.slice(0, limit);
  };

  const getSessionStats = () => {
    return {
      averageSessionDuration: pageAnalyticsData.behavior.sessions.reduce(
        (acc, session) => acc + (session.session_duration || 0), 0
      ) / (pageAnalyticsData.behavior.sessions.length || 1),
      averagePagesPerSession: pageAnalyticsData.behavior.sessions.reduce(
        (acc, session) => acc + (session.pages_visited || 0), 0
      ) / (pageAnalyticsData.behavior.sessions.length || 1),
      totalSessions: pageAnalyticsData.behavior.sessions.length
    };
  };

  const getTopInteractions = (limit = 10) => {
    return pageAnalyticsData.behavior.interactions.slice(0, limit);
  };

  return (
    <AllContext.Provider
      value={{
        isViewerOpen,
        setIsViewerOpen,
        selectedImage,
        setSelectedImage,
        unReadChat,
        setUnReadChat,
        selectedMobileNumber,
        setSelectedMobileNumber,
        chats,
        setChats,
        convpage,
        setConvPage,
        scrolarinmiddle,
        setScrolarinmiddle,
        page,
        setPage,
        allChats,
        setAllChats,
        starChats,
        setStarChats,
        checkboxList,
        setCheckboxList,
        chatsLoading,
        setChatsLoading,
        selectedName,
        setSelectedName,
        isOldMsg,
        setIsOldMsg,
        remainingTime,
        setRemainingTime,
        sendTemplatePopUp,
        setSendTemplatePopUp,
        wpProfile,
        setWpProfile,
        callData,
        setCallData,
        socket,
        text,
        setText,
        handleChatOpen,
        waitingChats,
        setWaitingChats,
        filteredChats,
        setFilteredChats,
        selectedUserDetails,
        setSelectedUserDetails,
        reply,
        setReply,
        mobileVisible,
        setMobileVisible,
        breakInfo,
        startBreak,
        endBreak,
        pipelines,
        setPipelines,
        activePipelineId,
        setActivePipelineId,
        fetchAgents,
        agents,
        channel,
        setChannel,
        chatCategory,
        setChatCategory,
        chatFilterType,
        setChatFilterType,
        roleFilterType,
        setRoleFilterType,
        nextConvId,
        setNextConvId,
        selectedChannel,
        setSelectedChannel,
        setUnreadCount,
        unreadCount,
        loading,
        setLoading,
        
        // Enhanced online users data with analytics (unchanged functionality)
        onlineUsersData,
        setOnlineUsersData,
        onlineUsersSocket,
        
        // ✅ Real-time Page Analytics data and functions
        pageAnalyticsData,
        setPageAnalyticsData,
        analyticsFilters,
        setAnalyticsFilters,
        
        // ✅ NEW: Real-time refresh function
        handleManualRefresh,
        
        // ✅ KEEP: Historical data functions (API-based for historical dates only)
        fetchAnalyticsOverview,
        fetchPageAnalytics,
        fetchBehaviorAnalytics,
        loadAllAnalytics,
        updateAnalyticsFilters,
        
        // Historical data functionality (unchanged)
        historicalMode,
        setHistoricalMode,
        selectedDate,
        setSelectedDate,
        availableDates,
        setAvailableDates,
        historicalData,
        setHistoricalData,
        fetchAvailableDates,
        fetchHistoricalData,
        switchToHistoricalMode,
        switchToRealTimeMode,
        
        // Helper functions for easier data access (unchanged)
        getTopCountries,
        getTopCities,
        getUsersByCountry,
        getUsersByCity,
        
        // ✅ Page analytics helper functions
        getTopPerformingPages,
        getSessionStats,
        getTopInteractions,
      }}
    >
      {children}
    </AllContext.Provider>
  );
};

export const ChatState = () => {
  return useContext(AllContext);
};

export default AllProvider;
