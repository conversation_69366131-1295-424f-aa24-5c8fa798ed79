{"ast": null, "code": "var _jsxFileName = \"D:\\\\DataGen\\\\authkey-chat-new\\\\src\\\\components\\\\ReportSections\\\\PageAnalyticsDashboard.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useState, useEffect } from 'react';\nimport { ChatState } from '../../context/AllProviders';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst PageAnalyticsDashboard = () => {\n  _s();\n\n  var _websiteOptions$find, _websiteOptions$find2;\n\n  const {\n    pageAnalyticsData,\n    onlineUsersData,\n    loading\n  } = ChatState();\n  const [refreshing, setRefreshing] = useState(false);\n  const [selectedWebsite, setSelectedWebsite] = useState('all');\n  const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false); // Utility functions\n\n  const formatNumber = num => {\n    const numValue = Number(num);\n    if (isNaN(numValue) || !isFinite(numValue)) return '0';\n    if (numValue >= 1000000) return (numValue / 1000000).toFixed(1) + 'M';\n    if (numValue >= 1000) return (numValue / 1000).toFixed(1) + 'K';\n    return numValue.toString();\n  };\n\n  const safeGetValue = function (value) {\n    let fallback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    const numValue = Number(value);\n    return isNaN(numValue) || !isFinite(numValue) ? fallback : numValue;\n  };\n\n  const getEventColor = eventType => {\n    const colors = {\n      'click': '#3B82F6',\n      'page_view': '#10B981',\n      'widget_open': '#8B5CF6',\n      'widget_close': '#8B5CF6',\n      'whatsapp_send_click': '#25D366',\n      'auto_widget_open': '#F59E0B',\n      'button_click': '#EF4444',\n      'link_click': '#06B6D4',\n      'scroll': '#84CC16',\n      'page_enter': '#10B981',\n      'page_exit': '#F97316'\n    };\n    return colors[eventType] || '#6B7280';\n  };\n\n  const formatEventName = eventType => {\n    const names = {\n      'whatsapp_send_click': 'WhatsApp Click',\n      'auto_widget_open': 'Auto Widget Open',\n      'widget_open': 'Widget Open',\n      'widget_close': 'Widget Close',\n      'button_click': 'Button Click',\n      'link_click': 'Link Click',\n      'page_view': 'Page View',\n      'page_enter': 'Page Enter',\n      'page_exit': 'Page Exit'\n    };\n    return names[eventType] || eventType.replace('_', ' ');\n  }; // ✅ CRITICAL FIX: Website filtering logic\n\n\n  const websiteBreakdown = onlineUsersData.websiteWidgetBreakdown || [];\n  const websiteOptions = [{\n    value: 'all',\n    label: 'All Websites',\n    count: onlineUsersData.totalOnline || 0\n  }, ...websiteBreakdown.map(item => ({\n    value: `${item.websiteUrl}|${item.widgetId}`,\n    label: item.websiteUrl || 'Unknown Website',\n    widgetId: item.widgetId,\n    count: item.userCount\n  }))]; // Close dropdown when clicking outside\n\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showWebsiteDropdown && !event.target.closest('.website-dropdown-container')) {\n        setShowWebsiteDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showWebsiteDropdown]);\n\n  const ConnectionStatus = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex align-items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `rounded-circle ${onlineUsersData.isConnected ? 'bg-success' : 'bg-danger'}`,\n        style: {\n          width: '10px',\n          height: '10px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), onlineUsersData.isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute top-0 start-0 rounded-circle bg-success animate-ping\",\n        style: {\n          width: '10px',\n          height: '10px',\n          opacity: '0.75'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `fw-medium ${onlineUsersData.isConnected ? 'text-success' : 'text-danger'}`,\n      style: {\n        fontSize: '14px'\n      },\n      children: onlineUsersData.isConnected ? 'Live' : 'Offline'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n\n  const TopPages = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"position-relative overflow-hidden\",\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n      borderRadius: '20px',\n      border: '1px solid rgba(226, 232, 240, 0.8)',\n      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n      backdropFilter: 'blur(10px)',\n      height: '480px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-bottom\",\n      style: {\n        borderColor: 'rgba(226, 232, 240, 0.5)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-1 fw-bold\",\n            style: {\n              color: '#1e293b',\n              fontSize: '18px'\n            },\n            children: \"Active Pages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0 text-muted\",\n            style: {\n              fontSize: '13px'\n            },\n            children: \"Real-time user activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"badge d-flex align-items-center gap-1 px-3 py-2\",\n          style: {\n            background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n            border: 'none',\n            borderRadius: '12px',\n            color: 'white',\n            fontSize: '12px',\n            fontWeight: '600'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: (onlineUsersData.topPages || []).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"pages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      style: {\n        height: 'calc(100% - 80px)',\n        overflowY: 'auto'\n      },\n      children: (onlineUsersData.topPages || []).length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex flex-column gap-3\",\n        children: (onlineUsersData.topPages || []).slice(0, 6).map((page, index) => {\n          var _page$path;\n\n          const userCount = safeGetValue(page.users, 0);\n          const maxUsers = Math.max(...(onlineUsersData.topPages || []).map(p => safeGetValue(p.users, 0)), 1);\n          const widthPercentage = Math.min(100, userCount / maxUsers * 100);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"position-relative p-3 transition-all\",\n            style: {\n              background: 'rgba(255, 255, 255, 0.7)',\n              borderRadius: '16px',\n              border: '1px solid rgba(226, 232, 240, 0.6)',\n              backdropFilter: 'blur(8px)',\n              transition: 'all 0.3s ease',\n              cursor: 'pointer'\n            },\n            onMouseEnter: e => {\n              e.target.style.transform = 'translateY(-2px)';\n              e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\n            },\n            onMouseLeave: e => {\n              e.target.style.transform = 'translateY(0px)';\n              e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center justify-content-center\",\n                  style: {\n                    width: '32px',\n                    height: '32px',\n                    borderRadius: '10px',\n                    background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\n                    color: 'white',\n                    fontSize: '14px',\n                    fontWeight: '600'\n                  },\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 fw-semibold\",\n                    style: {\n                      fontSize: '14px',\n                      color: '#1e293b'\n                    },\n                    children: page.path === '/' ? 'Homepage' : (((_page$path = page.path) === null || _page$path === void 0 ? void 0 : _page$path.length) > 25 ? page.path.substring(0, 25) + '...' : page.path) || 'Unknown'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 text-muted\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [userCount, \" active \", userCount === 1 ? 'user' : 'users']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"badge d-flex align-items-center justify-content-center\",\n                style: {\n                  background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\n                  color: 'white',\n                  borderRadius: '12px',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  minWidth: '40px',\n                  height: '28px'\n                },\n                children: userCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative\",\n              style: {\n                height: '6px',\n                background: 'rgba(226, 232, 240, 0.5)',\n                borderRadius: '6px',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-absolute top-0 start-0 h-100\",\n                style: {\n                  width: `${widthPercentage}%`,\n                  background: `linear-gradient(90deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}cc)`,\n                  borderRadius: '6px',\n                  transition: 'width 0.5s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex flex-column align-items-center justify-content-center h-100 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3 d-flex align-items-center justify-content-center\",\n          style: {\n            width: '80px',\n            height: '80px',\n            borderRadius: '20px',\n            background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\n            color: '#64748b'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"32\",\n            height: \"32\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-2 fw-semibold\",\n          style: {\n            color: '#475569'\n          },\n          children: \"No Active Pages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0 text-muted\",\n          style: {\n            fontSize: '14px'\n          },\n          children: \"Page activity will appear here when users visit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n\n  const TopInteractions = () => {\n    var _pageAnalyticsData$be, _pageAnalyticsData$be2;\n\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-relative overflow-hidden\",\n      style: {\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n        borderRadius: '20px',\n        border: '1px solid rgba(226, 232, 240, 0.8)',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n        backdropFilter: 'blur(10px)',\n        height: '480px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-bottom\",\n        style: {\n          borderColor: 'rgba(226, 232, 240, 0.5)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-1 fw-bold\",\n              style: {\n                color: '#1e293b',\n                fontSize: '18px'\n              },\n              children: \"User Interactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-0 text-muted\",\n              style: {\n                fontSize: '13px'\n              },\n              children: \"Today's activity breakdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge d-flex align-items-center gap-1 px-3 py-2\",\n            style: {\n              background: 'linear-gradient(135deg, #10b981, #059669)',\n              border: 'none',\n              borderRadius: '12px',\n              color: 'white',\n              fontSize: '12px',\n              fontWeight: '600'\n            },\n            children: \"Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        style: {\n          height: 'calc(100% - 80px)',\n          overflowY: 'auto'\n        },\n        children: (((_pageAnalyticsData$be = pageAnalyticsData.behavior) === null || _pageAnalyticsData$be === void 0 ? void 0 : _pageAnalyticsData$be.interactions) || []).length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column gap-3\",\n          children: (((_pageAnalyticsData$be2 = pageAnalyticsData.behavior) === null || _pageAnalyticsData$be2 === void 0 ? void 0 : _pageAnalyticsData$be2.interactions) || []).slice(0, 7).map((interaction, index) => {\n            var _pageAnalyticsData$be3;\n\n            let count = safeGetValue(interaction.count, 0); // Divide page_exit count by 2 to fix double counting issue\n\n            if (interaction.event_type === 'page_exit') {\n              count = Math.round(count / 2);\n            }\n\n            const uniqueUsers = safeGetValue(interaction.unique_users, 0);\n            const maxCount = Math.max(...(((_pageAnalyticsData$be3 = pageAnalyticsData.behavior) === null || _pageAnalyticsData$be3 === void 0 ? void 0 : _pageAnalyticsData$be3.interactions) || []).map(i => safeGetValue(i.count, 0)), 1);\n            const widthPercentage = Math.min(100, count / maxCount * 100);\n            const eventColor = getEventColor(interaction.event_type);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative p-3 transition-all\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.7)',\n                borderRadius: '16px',\n                border: '1px solid rgba(226, 232, 240, 0.6)',\n                backdropFilter: 'blur(8px)',\n                transition: 'all 0.3s ease',\n                cursor: 'pointer'\n              },\n              onMouseEnter: e => {\n                e.target.style.transform = 'translateY(-2px)';\n                e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\n              },\n              onMouseLeave: e => {\n                e.target.style.transform = 'translateY(0px)';\n                e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center justify-content-center\",\n                    style: {\n                      width: '36px',\n                      height: '36px',\n                      borderRadius: '12px',\n                      background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\n                      color: 'white'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"m12 1 0 6m0 6 0 6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"m17 12-6 0m-6 0 6 0\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-0 fw-semibold\",\n                      style: {\n                        fontSize: '14px',\n                        color: '#1e293b'\n                      },\n                      children: formatEventName(interaction.event_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-0 text-muted\",\n                      style: {\n                        fontSize: '12px'\n                      },\n                      children: [uniqueUsers, \" unique \", uniqueUsers === 1 ? 'user' : 'users']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"badge d-flex align-items-center justify-content-center\",\n                  style: {\n                    background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\n                    color: 'white',\n                    borderRadius: '12px',\n                    fontSize: '12px',\n                    fontWeight: '600',\n                    minWidth: '45px',\n                    height: '28px'\n                  },\n                  children: formatNumber(count)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-relative\",\n                style: {\n                  height: '6px',\n                  background: 'rgba(226, 232, 240, 0.5)',\n                  borderRadius: '6px',\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"position-absolute top-0 start-0 h-100\",\n                  style: {\n                    width: `${widthPercentage}%`,\n                    background: `linear-gradient(90deg, ${eventColor}, ${eventColor}cc)`,\n                    borderRadius: '6px',\n                    transition: 'width 0.5s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column align-items-center justify-content-center h-100 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 d-flex align-items-center justify-content-center\",\n            style: {\n              width: '80px',\n              height: '80px',\n              borderRadius: '20px',\n              background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\n              color: '#64748b'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"32\",\n              height: \"32\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.5\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-2 fw-semibold\",\n            style: {\n              color: '#475569'\n            },\n            children: \"No Interactions Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0 text-muted\",\n            style: {\n              fontSize: '14px'\n            },\n            children: \"User interactions will be displayed here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 5\n    }, this);\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 position-relative\",\n    style: {\n      background: 'transparent',\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4 p-4\",\n      style: {\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '20px',\n        backdropFilter: 'blur(20px)',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-1 fw-bold\",\n            style: {\n              color: 'maroon',\n              fontSize: '24px'\n            },\n            children: \"Widget Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0 text-muted\",\n            style: {\n              fontSize: '14px'\n            },\n            children: \"Real-time insights and user behavior\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"position-relative website-dropdown-container\",\n          style: {\n            minWidth: '200px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline-secondary btn-sm d-flex justify-content-between align-items-center w-100\",\n            style: {\n              borderRadius: \"10px\",\n              fontSize: \"13px\",\n              fontWeight: \"500\",\n              padding: \"10px 15px\",\n              border: \"1px solid #dee2e6\",\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\"\n            },\n            onClick: () => setShowWebsiteDropdown(!showWebsiteDropdown),\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-truncate\",\n              children: [((_websiteOptions$find = websiteOptions.find(opt => opt.value === selectedWebsite)) === null || _websiteOptions$find === void 0 ? void 0 : _websiteOptions$find.label) || 'All Websites', selectedWebsite !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-muted ms-1\",\n                children: [\"(\", ((_websiteOptions$find2 = websiteOptions.find(opt => opt.value === selectedWebsite)) === null || _websiteOptions$find2 === void 0 ? void 0 : _websiteOptions$find2.count) || 0, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: \"10px\"\n              },\n              children: \"\\u25BC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), showWebsiteDropdown && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"position-absolute w-100\",\n            style: {\n              top: \"100%\",\n              left: 0,\n              zIndex: 1000,\n              backgroundColor: \"white\",\n              border: \"1px solid #dee2e6\",\n              borderRadius: \"10px\",\n              boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.15)\",\n              maxHeight: \"250px\",\n              overflowY: \"auto\",\n              marginTop: \"8px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-3 py-2 border-bottom\",\n              style: {\n                backgroundColor: \"#f8f9fa\",\n                fontSize: \"12px\",\n                fontWeight: \"600\",\n                color: \"#6c757d\",\n                borderRadius: \"10px 10px 0 0\"\n              },\n              children: [\"Select Website (\", websiteOptions.length, \" available)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), websiteOptions.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-3 py-3\",\n              style: {\n                cursor: \"pointer\",\n                fontSize: \"13px\",\n                borderBottom: index < websiteOptions.length - 1 ? \"1px solid #f1f3f4\" : \"none\",\n                backgroundColor: selectedWebsite === option.value ? \"#e3f2fd\" : \"transparent\",\n                borderRadius: index === websiteOptions.length - 1 ? \"0 0 10px 10px\" : \"0\"\n              },\n              onMouseEnter: e => {\n                if (selectedWebsite !== option.value) {\n                  e.target.style.backgroundColor = \"#f8f9fa\";\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedWebsite !== option.value) {\n                  e.target.style.backgroundColor = \"transparent\";\n                }\n              },\n              onClick: () => {\n                setSelectedWebsite(option.value);\n                setShowWebsiteDropdown(false);\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-truncate\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-semibold\",\n                    children: option.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 25\n                  }, this), option.widgetId && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted\",\n                    style: {\n                      fontSize: \"11px\"\n                    },\n                    children: [\"Widget: \", option.widgetId.substring(0, 8), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-primary me-2\",\n                    style: {\n                      fontSize: \"11px\"\n                    },\n                    children: option.count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 25\n                  }, this), selectedWebsite === option.value && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: \"#1976d2\",\n                      fontSize: \"12px\"\n                    },\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 21\n              }, this)\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row g-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-xl-6\",\n        children: /*#__PURE__*/_jsxDEV(TopPages, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-xl-6\",\n        children: /*#__PURE__*/_jsxDEV(TopInteractions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this), (loading || pageAnalyticsData.loading) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n      style: {\n        zIndex: 9999,\n        background: 'rgba(0, 0, 0, 0.4)',\n        backdropFilter: 'blur(8px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-4\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: '20px',\n          backdropFilter: 'blur(20px)',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border mb-3\",\n          style: {\n            width: '3rem',\n            height: '3rem',\n            color: '#667eea'\n          },\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0 fw-medium\",\n          style: {\n            color: '#1e293b'\n          },\n          children: \"Updating Analytics...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .animate-ping {\n          animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n        }\n        \n        @keyframes ping {\n          75%, 100% {\n            transform: scale(2);\n            opacity: 0;\n          }\n        }\n        \n        .transition-all {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n        \n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: rgba(226, 232, 240, 0.3);\n          border-radius: 6px;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: rgba(148, 163, 184, 0.5);\n          border-radius: 6px;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: rgba(148, 163, 184, 0.7);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 438,\n    columnNumber: 5\n  }, this);\n};\n\n_s(PageAnalyticsDashboard, \"nwbIEzLZufMT7IVa+/sf5/kzC/g=\");\n\n_c = PageAnalyticsDashboard;\nexport default PageAnalyticsDashboard;\n\nvar _c;\n\n$RefreshReg$(_c, \"PageAnalyticsDashboard\");", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/components/ReportSections/PageAnalyticsDashboard.jsx"], "names": ["React", "useState", "useEffect", "ChatState", "PageAnalyticsDashboard", "pageAnalyticsData", "onlineUsersData", "loading", "refreshing", "setRefreshing", "selectedWebsite", "setSelectedWebsite", "showWebsiteDropdown", "setShowWebsiteDropdown", "formatNumber", "num", "numValue", "Number", "isNaN", "isFinite", "toFixed", "toString", "safeGetValue", "value", "fallback", "getEventColor", "eventType", "colors", "formatEventName", "names", "replace", "websiteBreakdown", "websiteWidgetBreakdown", "websiteOptions", "label", "count", "totalOnline", "map", "item", "websiteUrl", "widgetId", "userCount", "handleClickOutside", "event", "target", "closest", "document", "addEventListener", "removeEventListener", "ConnectionStatus", "isConnected", "width", "height", "opacity", "fontSize", "TopPages", "background", "borderRadius", "border", "boxShadow", "<PERSON><PERSON>ilter", "borderColor", "color", "fontWeight", "topPages", "length", "overflowY", "slice", "page", "index", "users", "maxUsers", "Math", "max", "p", "widthPercentage", "min", "transition", "cursor", "e", "style", "transform", "path", "substring", "min<PERSON><PERSON><PERSON>", "overflow", "TopInteractions", "behavior", "interactions", "interaction", "event_type", "round", "uniqueUsers", "unique_users", "maxCount", "i", "eventColor", "padding", "backgroundColor", "find", "opt", "top", "left", "zIndex", "maxHeight", "marginTop", "option", "borderBottom"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,QAA2C,OAA3C;AACA,SAASC,SAAT,QAA0B,4BAA1B;;;AAEA,MAAMC,sBAAsB,GAAG,MAAM;AAAA;;AAAA;;AACnC,QAAM;AACJC,IAAAA,iBADI;AAEJC,IAAAA,eAFI;AAGJC,IAAAA;AAHI,MAIFJ,SAAS,EAJb;AAMA,QAAM,CAACK,UAAD,EAAaC,aAAb,IAA8BR,QAAQ,CAAC,KAAD,CAA5C;AACA,QAAM,CAACS,eAAD,EAAkBC,kBAAlB,IAAwCV,QAAQ,CAAC,KAAD,CAAtD;AACA,QAAM,CAACW,mBAAD,EAAsBC,sBAAtB,IAAgDZ,QAAQ,CAAC,KAAD,CAA9D,CATmC,CAWnC;;AACA,QAAMa,YAAY,GAAIC,GAAD,IAAS;AAC5B,UAAMC,QAAQ,GAAGC,MAAM,CAACF,GAAD,CAAvB;AACA,QAAIG,KAAK,CAACF,QAAD,CAAL,IAAmB,CAACG,QAAQ,CAACH,QAAD,CAAhC,EAA4C,OAAO,GAAP;AAE5C,QAAIA,QAAQ,IAAI,OAAhB,EAAyB,OAAO,CAACA,QAAQ,GAAG,OAAZ,EAAqBI,OAArB,CAA6B,CAA7B,IAAkC,GAAzC;AACzB,QAAIJ,QAAQ,IAAI,IAAhB,EAAsB,OAAO,CAACA,QAAQ,GAAG,IAAZ,EAAkBI,OAAlB,CAA0B,CAA1B,IAA+B,GAAtC;AACtB,WAAOJ,QAAQ,CAACK,QAAT,EAAP;AACD,GAPD;;AASA,QAAMC,YAAY,GAAG,UAACC,KAAD,EAAyB;AAAA,QAAjBC,QAAiB,uEAAN,CAAM;AAC5C,UAAMR,QAAQ,GAAGC,MAAM,CAACM,KAAD,CAAvB;AACA,WAAOL,KAAK,CAACF,QAAD,CAAL,IAAmB,CAACG,QAAQ,CAACH,QAAD,CAA5B,GAAyCQ,QAAzC,GAAoDR,QAA3D;AACD,GAHD;;AAKA,QAAMS,aAAa,GAAIC,SAAD,IAAe;AACnC,UAAMC,MAAM,GAAG;AACb,eAAS,SADI;AAEb,mBAAa,SAFA;AAGb,qBAAe,SAHF;AAIb,sBAAgB,SAJH;AAKb,6BAAuB,SALV;AAMb,0BAAoB,SANP;AAOb,sBAAgB,SAPH;AAQb,oBAAc,SARD;AASb,gBAAU,SATG;AAUb,oBAAc,SAVD;AAWb,mBAAa;AAXA,KAAf;AAaA,WAAOA,MAAM,CAACD,SAAD,CAAN,IAAqB,SAA5B;AACD,GAfD;;AAiBA,QAAME,eAAe,GAAIF,SAAD,IAAe;AACrC,UAAMG,KAAK,GAAG;AACZ,6BAAuB,gBADX;AAEZ,0BAAoB,kBAFR;AAGZ,qBAAe,aAHH;AAIZ,sBAAgB,cAJJ;AAKZ,sBAAgB,cALJ;AAMZ,oBAAc,YANF;AAOZ,mBAAa,WAPD;AAQZ,oBAAc,YARF;AASZ,mBAAa;AATD,KAAd;AAWA,WAAOA,KAAK,CAACH,SAAD,CAAL,IAAoBA,SAAS,CAACI,OAAV,CAAkB,GAAlB,EAAuB,GAAvB,CAA3B;AACD,GAbD,CA3CmC,CA0DnC;;;AACA,QAAMC,gBAAgB,GAAGzB,eAAe,CAAC0B,sBAAhB,IAA0C,EAAnE;AACA,QAAMC,cAAc,GAAG,CACrB;AAAEV,IAAAA,KAAK,EAAE,KAAT;AAAgBW,IAAAA,KAAK,EAAE,cAAvB;AAAuCC,IAAAA,KAAK,EAAE7B,eAAe,CAAC8B,WAAhB,IAA+B;AAA7E,GADqB,EAErB,GAAGL,gBAAgB,CAACM,GAAjB,CAAqBC,IAAI,KAAK;AAC/Bf,IAAAA,KAAK,EAAG,GAAEe,IAAI,CAACC,UAAW,IAAGD,IAAI,CAACE,QAAS,EADZ;AAE/BN,IAAAA,KAAK,EAAEI,IAAI,CAACC,UAAL,IAAmB,iBAFK;AAG/BC,IAAAA,QAAQ,EAAEF,IAAI,CAACE,QAHgB;AAI/BL,IAAAA,KAAK,EAAEG,IAAI,CAACG;AAJmB,GAAL,CAAzB,CAFkB,CAAvB,CA5DmC,CAsEnC;;AACAvC,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMwC,kBAAkB,GAAIC,KAAD,IAAW;AACpC,UAAI/B,mBAAmB,IAAI,CAAC+B,KAAK,CAACC,MAAN,CAAaC,OAAb,CAAqB,6BAArB,CAA5B,EAAiF;AAC/EhC,QAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACD;AACF,KAJD;;AAMAiC,IAAAA,QAAQ,CAACC,gBAAT,CAA0B,WAA1B,EAAuCL,kBAAvC;AACA,WAAO,MAAM;AACXI,MAAAA,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0CN,kBAA1C;AACD,KAFD;AAGD,GAXQ,EAWN,CAAC9B,mBAAD,CAXM,CAAT;;AAaA,QAAMqC,gBAAgB,GAAG,mBACvB;AAAK,IAAA,SAAS,EAAC,iCAAf;AAAA,4BACE;AAAK,MAAA,SAAS,EAAC,mBAAf;AAAA,8BACE;AACE,QAAA,SAAS,EAAG,kBAAiB3C,eAAe,CAAC4C,WAAhB,GAA8B,YAA9B,GAA6C,WAAY,EADxF;AAEE,QAAA,KAAK,EAAE;AAAEC,UAAAA,KAAK,EAAE,MAAT;AAAiBC,UAAAA,MAAM,EAAE;AAAzB;AAFT;AAAA;AAAA;AAAA;AAAA,cADF,EAKG9C,eAAe,CAAC4C,WAAhB,iBACC;AACE,QAAA,SAAS,EAAC,wEADZ;AAEE,QAAA,KAAK,EAAE;AAAEC,UAAAA,KAAK,EAAE,MAAT;AAAiBC,UAAAA,MAAM,EAAE,MAAzB;AAAiCC,UAAAA,OAAO,EAAE;AAA1C;AAFT;AAAA;AAAA;AAAA;AAAA,cANJ;AAAA;AAAA;AAAA;AAAA;AAAA,YADF,eAaE;AAAM,MAAA,SAAS,EAAG,aAAY/C,eAAe,CAAC4C,WAAhB,GAA8B,cAA9B,GAA+C,aAAc,EAA3F;AAA8F,MAAA,KAAK,EAAE;AAAEI,QAAAA,QAAQ,EAAE;AAAZ,OAArG;AAAA,gBACGhD,eAAe,CAAC4C,WAAhB,GAA8B,MAA9B,GAAuC;AAD1C;AAAA;AAAA;AAAA;AAAA,YAbF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;;AAoBA,QAAMK,QAAQ,GAAG,mBACf;AACE,IAAA,SAAS,EAAC,mCADZ;AAEE,IAAA,KAAK,EAAE;AACLC,MAAAA,UAAU,EAAE,mDADP;AAELC,MAAAA,YAAY,EAAE,MAFT;AAGLC,MAAAA,MAAM,EAAE,oCAHH;AAILC,MAAAA,SAAS,EAAE,uEAJN;AAKLC,MAAAA,cAAc,EAAE,YALX;AAMLR,MAAAA,MAAM,EAAE;AANH,KAFT;AAAA,4BAYE;AAAK,MAAA,SAAS,EAAC,mBAAf;AAAmC,MAAA,KAAK,EAAE;AAAES,QAAAA,WAAW,EAAE;AAAf,OAA1C;AAAA,6BACE;AAAK,QAAA,SAAS,EAAC,mDAAf;AAAA,gCACE;AAAA,kCACE;AAAI,YAAA,SAAS,EAAC,cAAd;AAA6B,YAAA,KAAK,EAAE;AAAEC,cAAAA,KAAK,EAAE,SAAT;AAAoBR,cAAAA,QAAQ,EAAE;AAA9B,aAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eAIE;AAAG,YAAA,SAAS,EAAC,iBAAb;AAA+B,YAAA,KAAK,EAAE;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eASE;AACE,UAAA,SAAS,EAAC,iDADZ;AAEE,UAAA,KAAK,EAAE;AACLE,YAAAA,UAAU,EAAE,2CADP;AAELE,YAAAA,MAAM,EAAE,MAFH;AAGLD,YAAAA,YAAY,EAAE,MAHT;AAILK,YAAAA,KAAK,EAAE,OAJF;AAKLR,YAAAA,QAAQ,EAAE,MALL;AAMLS,YAAAA,UAAU,EAAE;AANP,WAFT;AAAA,kCAWE;AAAA,sBAAO,CAACzD,eAAe,CAAC0D,QAAhB,IAA4B,EAA7B,EAAiCC;AAAxC;AAAA;AAAA;AAAA;AAAA,kBAXF,eAYE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAZF;AAAA;AAAA;AAAA;AAAA;AAAA,gBATF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,YAZF,eAwCE;AAAK,MAAA,SAAS,EAAC,KAAf;AAAqB,MAAA,KAAK,EAAE;AAAEb,QAAAA,MAAM,EAAE,mBAAV;AAA+Bc,QAAAA,SAAS,EAAE;AAA1C,OAA5B;AAAA,gBACG,CAAC5D,eAAe,CAAC0D,QAAhB,IAA4B,EAA7B,EAAiCC,MAAjC,GAA0C,CAA1C,gBACC;AAAK,QAAA,SAAS,EAAC,0BAAf;AAAA,kBACG,CAAC3D,eAAe,CAAC0D,QAAhB,IAA4B,EAA7B,EAAiCG,KAAjC,CAAuC,CAAvC,EAA0C,CAA1C,EAA6C9B,GAA7C,CAAiD,CAAC+B,IAAD,EAAOC,KAAP,KAAiB;AAAA;;AACjE,gBAAM5B,SAAS,GAAGnB,YAAY,CAAC8C,IAAI,CAACE,KAAN,EAAa,CAAb,CAA9B;AACA,gBAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS,GAAG,CAACnE,eAAe,CAAC0D,QAAhB,IAA4B,EAA7B,EAAiC3B,GAAjC,CAAqCqC,CAAC,IAAIpD,YAAY,CAACoD,CAAC,CAACJ,KAAH,EAAU,CAAV,CAAtD,CAAZ,EAAiF,CAAjF,CAAjB;AACA,gBAAMK,eAAe,GAAGH,IAAI,CAACI,GAAL,CAAS,GAAT,EAAenC,SAAS,GAAG8B,QAAb,GAAyB,GAAvC,CAAxB;AAEA,8BACE;AAEE,YAAA,SAAS,EAAC,sCAFZ;AAGE,YAAA,KAAK,EAAE;AACLf,cAAAA,UAAU,EAAE,0BADP;AAELC,cAAAA,YAAY,EAAE,MAFT;AAGLC,cAAAA,MAAM,EAAE,oCAHH;AAILE,cAAAA,cAAc,EAAE,WAJX;AAKLiB,cAAAA,UAAU,EAAE,eALP;AAMLC,cAAAA,MAAM,EAAE;AANH,aAHT;AAWE,YAAA,YAAY,EAAGC,CAAD,IAAO;AACnBA,cAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAeC,SAAf,GAA2B,kBAA3B;AACAF,cAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAerB,SAAf,GAA2B,oCAA3B;AACD,aAdH;AAeE,YAAA,YAAY,EAAGoB,CAAD,IAAO;AACnBA,cAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAeC,SAAf,GAA2B,iBAA3B;AACAF,cAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAerB,SAAf,GAA2B,gCAA3B;AACD,aAlBH;AAAA,oCAoBE;AAAK,cAAA,SAAS,EAAC,wDAAf;AAAA,sCACE;AAAK,gBAAA,SAAS,EAAC,iCAAf;AAAA,wCACE;AACE,kBAAA,SAAS,EAAC,kDADZ;AAEE,kBAAA,KAAK,EAAE;AACLR,oBAAAA,KAAK,EAAE,MADF;AAELC,oBAAAA,MAAM,EAAE,MAFH;AAGLK,oBAAAA,YAAY,EAAE,MAHT;AAILD,oBAAAA,UAAU,EAAG,2BAA0B/B,aAAa,CAAC,WAAD,CAAc,KAAIA,aAAa,CAAC,WAAD,CAAc,KAJ5F;AAKLqC,oBAAAA,KAAK,EAAE,OALF;AAMLR,oBAAAA,QAAQ,EAAE,MANL;AAOLS,oBAAAA,UAAU,EAAE;AAPP,mBAFT;AAAA,4BAYGM,KAAK,GAAG;AAZX;AAAA;AAAA;AAAA;AAAA,wBADF,eAeE;AAAA,0CACE;AAAG,oBAAA,SAAS,EAAC,kBAAb;AAAgC,oBAAA,KAAK,EAAE;AAAEf,sBAAAA,QAAQ,EAAE,MAAZ;AAAoBQ,sBAAAA,KAAK,EAAE;AAA3B,qBAAvC;AAAA,8BACGM,IAAI,CAACc,IAAL,KAAc,GAAd,GAAoB,UAApB,GAAiC,CAAC,eAAAd,IAAI,CAACc,IAAL,0DAAWjB,MAAX,IAAoB,EAApB,GAAyBG,IAAI,CAACc,IAAL,CAAUC,SAAV,CAAoB,CAApB,EAAuB,EAAvB,IAA6B,KAAtD,GAA8Df,IAAI,CAACc,IAApE,KAA6E;AADjH;AAAA;AAAA;AAAA;AAAA,0BADF,eAIE;AAAG,oBAAA,SAAS,EAAC,iBAAb;AAA+B,oBAAA,KAAK,EAAE;AAAE5B,sBAAAA,QAAQ,EAAE;AAAZ,qBAAtC;AAAA,+BACGb,SADH,cACsBA,SAAS,KAAK,CAAd,GAAkB,MAAlB,GAA2B,OADjD;AAAA;AAAA;AAAA;AAAA;AAAA,0BAJF;AAAA;AAAA;AAAA;AAAA;AAAA,wBAfF;AAAA;AAAA;AAAA;AAAA;AAAA,sBADF,eAyBE;AACE,gBAAA,SAAS,EAAC,wDADZ;AAEE,gBAAA,KAAK,EAAE;AACLe,kBAAAA,UAAU,EAAG,2BAA0B/B,aAAa,CAAC,WAAD,CAAc,KAAIA,aAAa,CAAC,WAAD,CAAc,KAD5F;AAELqC,kBAAAA,KAAK,EAAE,OAFF;AAGLL,kBAAAA,YAAY,EAAE,MAHT;AAILH,kBAAAA,QAAQ,EAAE,MAJL;AAKLS,kBAAAA,UAAU,EAAE,KALP;AAMLqB,kBAAAA,QAAQ,EAAE,MANL;AAOLhC,kBAAAA,MAAM,EAAE;AAPH,iBAFT;AAAA,0BAYGX;AAZH;AAAA;AAAA;AAAA;AAAA,sBAzBF;AAAA;AAAA;AAAA;AAAA;AAAA,oBApBF,eA8DE;AACE,cAAA,SAAS,EAAC,mBADZ;AAEE,cAAA,KAAK,EAAE;AACLW,gBAAAA,MAAM,EAAE,KADH;AAELI,gBAAAA,UAAU,EAAE,0BAFP;AAGLC,gBAAAA,YAAY,EAAE,KAHT;AAIL4B,gBAAAA,QAAQ,EAAE;AAJL,eAFT;AAAA,qCASE;AACE,gBAAA,SAAS,EAAC,uCADZ;AAEE,gBAAA,KAAK,EAAE;AACLlC,kBAAAA,KAAK,EAAG,GAAEwB,eAAgB,GADrB;AAELnB,kBAAAA,UAAU,EAAG,0BAAyB/B,aAAa,CAAC,WAAD,CAAc,KAAIA,aAAa,CAAC,WAAD,CAAc,KAF3F;AAGLgC,kBAAAA,YAAY,EAAE,KAHT;AAILoB,kBAAAA,UAAU,EAAE;AAJP;AAFT;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA,oBA9DF;AAAA,aACOR,KADP;AAAA;AAAA;AAAA;AAAA,kBADF;AAoFD,SAzFA;AADH;AAAA;AAAA;AAAA;AAAA,cADD,gBA8FC;AAAK,QAAA,SAAS,EAAC,gFAAf;AAAA,gCACE;AACE,UAAA,SAAS,EAAC,uDADZ;AAEE,UAAA,KAAK,EAAE;AACLlB,YAAAA,KAAK,EAAE,MADF;AAELC,YAAAA,MAAM,EAAE,MAFH;AAGLK,YAAAA,YAAY,EAAE,MAHT;AAILD,YAAAA,UAAU,EAAE,2CAJP;AAKLM,YAAAA,KAAK,EAAE;AALF,WAFT;AAAA,iCAUE;AAAK,YAAA,KAAK,EAAC,IAAX;AAAgB,YAAA,MAAM,EAAC,IAAvB;AAA4B,YAAA,OAAO,EAAC,WAApC;AAAgD,YAAA,IAAI,EAAC,MAArD;AAA4D,YAAA,MAAM,EAAC,cAAnE;AAAkF,YAAA,WAAW,EAAC,KAA9F;AAAA,mCACE;AAAM,cAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AAVF;AAAA;AAAA;AAAA;AAAA,gBADF,eAeE;AAAI,UAAA,SAAS,EAAC,kBAAd;AAAiC,UAAA,KAAK,EAAE;AAAEA,YAAAA,KAAK,EAAE;AAAT,WAAxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAfF,eAgBE;AAAG,UAAA,SAAS,EAAC,iBAAb;AAA+B,UAAA,KAAK,EAAE;AAAER,YAAAA,QAAQ,EAAE;AAAZ,WAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAhBF;AAAA;AAAA;AAAA;AAAA;AAAA;AA/FJ;AAAA;AAAA;AAAA;AAAA,YAxCF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;;AAiKA,QAAMgC,eAAe,GAAG;AAAA;;AAAA,wBACtB;AACE,MAAA,SAAS,EAAC,mCADZ;AAEE,MAAA,KAAK,EAAE;AACL9B,QAAAA,UAAU,EAAE,mDADP;AAELC,QAAAA,YAAY,EAAE,MAFT;AAGLC,QAAAA,MAAM,EAAE,oCAHH;AAILC,QAAAA,SAAS,EAAE,uEAJN;AAKLC,QAAAA,cAAc,EAAE,YALX;AAMLR,QAAAA,MAAM,EAAE;AANH,OAFT;AAAA,8BAYE;AAAK,QAAA,SAAS,EAAC,mBAAf;AAAmC,QAAA,KAAK,EAAE;AAAES,UAAAA,WAAW,EAAE;AAAf,SAA1C;AAAA,+BACE;AAAK,UAAA,SAAS,EAAC,mDAAf;AAAA,kCACE;AAAA,oCACE;AAAI,cAAA,SAAS,EAAC,cAAd;AAA6B,cAAA,KAAK,EAAE;AAAEC,gBAAAA,KAAK,EAAE,SAAT;AAAoBR,gBAAAA,QAAQ,EAAE;AAA9B,eAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBADF,eAIE;AAAG,cAAA,SAAS,EAAC,iBAAb;AAA+B,cAAA,KAAK,EAAE;AAAEA,gBAAAA,QAAQ,EAAE;AAAZ,eAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eASE;AACE,YAAA,SAAS,EAAC,iDADZ;AAEE,YAAA,KAAK,EAAE;AACLE,cAAAA,UAAU,EAAE,2CADP;AAELE,cAAAA,MAAM,EAAE,MAFH;AAGLD,cAAAA,YAAY,EAAE,MAHT;AAILK,cAAAA,KAAK,EAAE,OAJF;AAKLR,cAAAA,QAAQ,EAAE,MALL;AAMLS,cAAAA,UAAU,EAAE;AANP,aAFT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBATF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cAZF,eAuCE;AAAK,QAAA,SAAS,EAAC,KAAf;AAAqB,QAAA,KAAK,EAAE;AAAEX,UAAAA,MAAM,EAAE,mBAAV;AAA+Bc,UAAAA,SAAS,EAAE;AAA1C,SAA5B;AAAA,kBACG,CAAC,0BAAA7D,iBAAiB,CAACkF,QAAlB,gFAA4BC,YAA5B,KAA4C,EAA7C,EAAiDvB,MAAjD,GAA0D,CAA1D,gBACC;AAAK,UAAA,SAAS,EAAC,0BAAf;AAAA,oBACG,CAAC,2BAAA5D,iBAAiB,CAACkF,QAAlB,kFAA4BC,YAA5B,KAA4C,EAA7C,EAAiDrB,KAAjD,CAAuD,CAAvD,EAA0D,CAA1D,EAA6D9B,GAA7D,CAAiE,CAACoD,WAAD,EAAcpB,KAAd,KAAwB;AAAA;;AACxF,gBAAIlC,KAAK,GAAGb,YAAY,CAACmE,WAAW,CAACtD,KAAb,EAAoB,CAApB,CAAxB,CADwF,CAExF;;AACA,gBAAIsD,WAAW,CAACC,UAAZ,KAA2B,WAA/B,EAA4C;AAC1CvD,cAAAA,KAAK,GAAGqC,IAAI,CAACmB,KAAL,CAAWxD,KAAK,GAAG,CAAnB,CAAR;AACD;;AACD,kBAAMyD,WAAW,GAAGtE,YAAY,CAACmE,WAAW,CAACI,YAAb,EAA2B,CAA3B,CAAhC;AACA,kBAAMC,QAAQ,GAAGtB,IAAI,CAACC,GAAL,CAAS,GAAG,CAAC,2BAAApE,iBAAiB,CAACkF,QAAlB,kFAA4BC,YAA5B,KAA4C,EAA7C,EAAiDnD,GAAjD,CAAqD0D,CAAC,IAAIzE,YAAY,CAACyE,CAAC,CAAC5D,KAAH,EAAU,CAAV,CAAtE,CAAZ,EAAiG,CAAjG,CAAjB;AACA,kBAAMwC,eAAe,GAAGH,IAAI,CAACI,GAAL,CAAS,GAAT,EAAezC,KAAK,GAAG2D,QAAT,GAAqB,GAAnC,CAAxB;AACA,kBAAME,UAAU,GAAGvE,aAAa,CAACgE,WAAW,CAACC,UAAb,CAAhC;AAEA,gCACE;AAEE,cAAA,SAAS,EAAC,sCAFZ;AAGE,cAAA,KAAK,EAAE;AACLlC,gBAAAA,UAAU,EAAE,0BADP;AAELC,gBAAAA,YAAY,EAAE,MAFT;AAGLC,gBAAAA,MAAM,EAAE,oCAHH;AAILE,gBAAAA,cAAc,EAAE,WAJX;AAKLiB,gBAAAA,UAAU,EAAE,eALP;AAMLC,gBAAAA,MAAM,EAAE;AANH,eAHT;AAWE,cAAA,YAAY,EAAGC,CAAD,IAAO;AACnBA,gBAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAeC,SAAf,GAA2B,kBAA3B;AACAF,gBAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAerB,SAAf,GAA2B,oCAA3B;AACD,eAdH;AAeE,cAAA,YAAY,EAAGoB,CAAD,IAAO;AACnBA,gBAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAeC,SAAf,GAA2B,iBAA3B;AACAF,gBAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAerB,SAAf,GAA2B,gCAA3B;AACD,eAlBH;AAAA,sCAoBE;AAAK,gBAAA,SAAS,EAAC,wDAAf;AAAA,wCACE;AAAK,kBAAA,SAAS,EAAC,iCAAf;AAAA,0CACE;AACE,oBAAA,SAAS,EAAC,kDADZ;AAEE,oBAAA,KAAK,EAAE;AACLR,sBAAAA,KAAK,EAAE,MADF;AAELC,sBAAAA,MAAM,EAAE,MAFH;AAGLK,sBAAAA,YAAY,EAAE,MAHT;AAILD,sBAAAA,UAAU,EAAG,2BAA0BwC,UAAW,KAAIA,UAAW,KAJ5D;AAKLlC,sBAAAA,KAAK,EAAE;AALF,qBAFT;AAAA,2CAUE;AAAK,sBAAA,KAAK,EAAC,IAAX;AAAgB,sBAAA,MAAM,EAAC,IAAvB;AAA4B,sBAAA,OAAO,EAAC,WAApC;AAAgD,sBAAA,IAAI,EAAC,MAArD;AAA4D,sBAAA,MAAM,EAAC,cAAnE;AAAkF,sBAAA,WAAW,EAAC,GAA9F;AAAA,8CACE;AAAQ,wBAAA,EAAE,EAAC,IAAX;AAAgB,wBAAA,EAAE,EAAC,IAAnB;AAAwB,wBAAA,CAAC,EAAC;AAA1B;AAAA;AAAA;AAAA;AAAA,8BADF,eAEE;AAAM,wBAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA,8BAFF,eAGE;AAAM,wBAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA,8BAHF;AAAA;AAAA;AAAA;AAAA;AAAA;AAVF;AAAA;AAAA;AAAA;AAAA,0BADF,eAiBE;AAAA,4CACE;AAAG,sBAAA,SAAS,EAAC,kBAAb;AAAgC,sBAAA,KAAK,EAAE;AAAER,wBAAAA,QAAQ,EAAE,MAAZ;AAAoBQ,wBAAAA,KAAK,EAAE;AAA3B,uBAAvC;AAAA,gCACGlC,eAAe,CAAC6D,WAAW,CAACC,UAAb;AADlB;AAAA;AAAA;AAAA;AAAA,4BADF,eAIE;AAAG,sBAAA,SAAS,EAAC,iBAAb;AAA+B,sBAAA,KAAK,EAAE;AAAEpC,wBAAAA,QAAQ,EAAE;AAAZ,uBAAtC;AAAA,iCACGsC,WADH,cACwBA,WAAW,KAAK,CAAhB,GAAoB,MAApB,GAA6B,OADrD;AAAA;AAAA;AAAA;AAAA;AAAA,4BAJF;AAAA;AAAA;AAAA;AAAA;AAAA,0BAjBF;AAAA;AAAA;AAAA;AAAA;AAAA,wBADF,eA2BE;AACE,kBAAA,SAAS,EAAC,wDADZ;AAEE,kBAAA,KAAK,EAAE;AACLpC,oBAAAA,UAAU,EAAG,2BAA0BwC,UAAW,KAAIA,UAAW,KAD5D;AAELlC,oBAAAA,KAAK,EAAE,OAFF;AAGLL,oBAAAA,YAAY,EAAE,MAHT;AAILH,oBAAAA,QAAQ,EAAE,MAJL;AAKLS,oBAAAA,UAAU,EAAE,KALP;AAMLqB,oBAAAA,QAAQ,EAAE,MANL;AAOLhC,oBAAAA,MAAM,EAAE;AAPH,mBAFT;AAAA,4BAYGtC,YAAY,CAACqB,KAAD;AAZf;AAAA;AAAA;AAAA;AAAA,wBA3BF;AAAA;AAAA;AAAA;AAAA;AAAA,sBApBF,eAgEE;AACE,gBAAA,SAAS,EAAC,mBADZ;AAEE,gBAAA,KAAK,EAAE;AACLiB,kBAAAA,MAAM,EAAE,KADH;AAELI,kBAAAA,UAAU,EAAE,0BAFP;AAGLC,kBAAAA,YAAY,EAAE,KAHT;AAIL4B,kBAAAA,QAAQ,EAAE;AAJL,iBAFT;AAAA,uCASE;AACE,kBAAA,SAAS,EAAC,uCADZ;AAEE,kBAAA,KAAK,EAAE;AACLlC,oBAAAA,KAAK,EAAG,GAAEwB,eAAgB,GADrB;AAELnB,oBAAAA,UAAU,EAAG,0BAAyBwC,UAAW,KAAIA,UAAW,KAF3D;AAGLvC,oBAAAA,YAAY,EAAE,KAHT;AAILoB,oBAAAA,UAAU,EAAE;AAJP;AAFT;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA,sBAhEF;AAAA,eACOR,KADP;AAAA;AAAA;AAAA;AAAA,oBADF;AAsFD,WAjGA;AADH;AAAA;AAAA;AAAA;AAAA,gBADD,gBAsGC;AAAK,UAAA,SAAS,EAAC,gFAAf;AAAA,kCACE;AACE,YAAA,SAAS,EAAC,uDADZ;AAEE,YAAA,KAAK,EAAE;AACLlB,cAAAA,KAAK,EAAE,MADF;AAELC,cAAAA,MAAM,EAAE,MAFH;AAGLK,cAAAA,YAAY,EAAE,MAHT;AAILD,cAAAA,UAAU,EAAE,2CAJP;AAKLM,cAAAA,KAAK,EAAE;AALF,aAFT;AAAA,mCAUE;AAAK,cAAA,KAAK,EAAC,IAAX;AAAgB,cAAA,MAAM,EAAC,IAAvB;AAA4B,cAAA,OAAO,EAAC,WAApC;AAAgD,cAAA,IAAI,EAAC,MAArD;AAA4D,cAAA,MAAM,EAAC,cAAnE;AAAkF,cAAA,WAAW,EAAC,KAA9F;AAAA,qCACE;AAAM,gBAAA,CAAC,EAAC;AAAR;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AAVF;AAAA;AAAA;AAAA;AAAA,kBADF,eAeE;AAAI,YAAA,SAAS,EAAC,kBAAd;AAAiC,YAAA,KAAK,EAAE;AAAEA,cAAAA,KAAK,EAAE;AAAT,aAAxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAfF,eAgBE;AAAG,YAAA,SAAS,EAAC,iBAAb;AAA+B,YAAA,KAAK,EAAE;AAAER,cAAAA,QAAQ,EAAE;AAAZ,aAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAhBF;AAAA;AAAA;AAAA;AAAA;AAAA;AAvGJ;AAAA;AAAA;AAAA;AAAA,cAvCF;AAAA;AAAA;AAAA;AAAA;AAAA,YADsB;AAAA,GAAxB;;AAwKA,sBACE;AACE,IAAA,SAAS,EAAC,8BADZ;AAEE,IAAA,KAAK,EAAE;AACLE,MAAAA,UAAU,EAAE,aADP;AAELyC,MAAAA,OAAO,EAAE;AAFJ,KAFT;AAAA,4BAQE;AACE,MAAA,SAAS,EAAC,4DADZ;AAEE,MAAA,KAAK,EAAE;AACLzC,QAAAA,UAAU,EAAE,2BADP;AAELC,QAAAA,YAAY,EAAE,MAFT;AAGLG,QAAAA,cAAc,EAAE,YAHX;AAILF,QAAAA,MAAM,EAAE,oCAJH;AAKLC,QAAAA,SAAS,EAAE;AALN,OAFT;AAAA,8BAUE;AAAK,QAAA,SAAS,EAAC,iCAAf;AAAA,gCACE;AAAA,kCACE;AAAI,YAAA,SAAS,EAAC,cAAd;AAA6B,YAAA,KAAK,EAAE;AAAEG,cAAAA,KAAK,EAAE,QAAT;AAAmBR,cAAAA,QAAQ,EAAE;AAA7B,aAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eAIE;AAAG,YAAA,SAAS,EAAC,iBAAb;AAA+B,YAAA,KAAK,EAAE;AAAEA,cAAAA,QAAQ,EAAE;AAAZ,aAAtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAWE;AAAK,UAAA,SAAS,EAAC,8CAAf;AAA8D,UAAA,KAAK,EAAE;AAAE8B,YAAAA,QAAQ,EAAE;AAAZ,WAArE;AAAA,kCACE;AACE,YAAA,SAAS,EAAC,0FADZ;AAEE,YAAA,KAAK,EAAE;AACL3B,cAAAA,YAAY,EAAE,MADT;AAELH,cAAAA,QAAQ,EAAE,MAFL;AAGLS,cAAAA,UAAU,EAAE,KAHP;AAILkC,cAAAA,OAAO,EAAE,WAJJ;AAKLvC,cAAAA,MAAM,EAAE,mBALH;AAMLwC,cAAAA,eAAe,EAAE;AANZ,aAFT;AAUE,YAAA,OAAO,EAAE,MAAMrF,sBAAsB,CAAC,CAACD,mBAAF,CAVvC;AAWE,YAAA,QAAQ,EAAEL,OAXZ;AAAA,oCAaE;AAAM,cAAA,SAAS,EAAC,eAAhB;AAAA,yBACG,yBAAA0B,cAAc,CAACkE,IAAf,CAAoBC,GAAG,IAAIA,GAAG,CAAC7E,KAAJ,KAAcb,eAAzC,+EAA2DwB,KAA3D,KAAoE,cADvE,EAEGxB,eAAe,KAAK,KAApB,iBACC;AAAM,gBAAA,SAAS,EAAC,iBAAhB;AAAA,gCACI,0BAAAuB,cAAc,CAACkE,IAAf,CAAoBC,GAAG,IAAIA,GAAG,CAAC7E,KAAJ,KAAcb,eAAzC,iFAA2DyB,KAA3D,KAAoE,CADxE;AAAA;AAAA;AAAA;AAAA;AAAA,sBAHJ;AAAA;AAAA;AAAA;AAAA;AAAA,oBAbF,eAqBE;AAAM,cAAA,KAAK,EAAE;AAAEmB,gBAAAA,QAAQ,EAAE;AAAZ,eAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBArBF;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,EA0BG1C,mBAAmB,IAAI,CAACL,OAAxB,iBACC;AAAK,YAAA,SAAS,EAAC,yBAAf;AAAyC,YAAA,KAAK,EAAE;AAC9C8F,cAAAA,GAAG,EAAE,MADyC;AAE9CC,cAAAA,IAAI,EAAE,CAFwC;AAG9CC,cAAAA,MAAM,EAAE,IAHsC;AAI9CL,cAAAA,eAAe,EAAE,OAJ6B;AAK9CxC,cAAAA,MAAM,EAAE,mBALsC;AAM9CD,cAAAA,YAAY,EAAE,MANgC;AAO9CE,cAAAA,SAAS,EAAE,gCAPmC;AAQ9C6C,cAAAA,SAAS,EAAE,OARmC;AAS9CtC,cAAAA,SAAS,EAAE,MATmC;AAU9CuC,cAAAA,SAAS,EAAE;AAVmC,aAAhD;AAAA,oCAYE;AAAK,cAAA,SAAS,EAAC,yBAAf;AAAyC,cAAA,KAAK,EAAE;AAC9CP,gBAAAA,eAAe,EAAE,SAD6B;AAE9C5C,gBAAAA,QAAQ,EAAE,MAFoC;AAG9CS,gBAAAA,UAAU,EAAE,KAHkC;AAI9CD,gBAAAA,KAAK,EAAE,SAJuC;AAK9CL,gBAAAA,YAAY,EAAE;AALgC,eAAhD;AAAA,6CAOmBxB,cAAc,CAACgC,MAPlC;AAAA;AAAA;AAAA;AAAA;AAAA,oBAZF,EAsBGhC,cAAc,CAACI,GAAf,CAAmB,CAACqE,MAAD,EAASrC,KAAT,kBAClB;AAEE,cAAA,SAAS,EAAC,WAFZ;AAGE,cAAA,KAAK,EAAE;AACLS,gBAAAA,MAAM,EAAE,SADH;AAELxB,gBAAAA,QAAQ,EAAE,MAFL;AAGLqD,gBAAAA,YAAY,EAAEtC,KAAK,GAAGpC,cAAc,CAACgC,MAAf,GAAwB,CAAhC,GAAoC,mBAApC,GAA0D,MAHnE;AAILiC,gBAAAA,eAAe,EAAExF,eAAe,KAAKgG,MAAM,CAACnF,KAA3B,GAAmC,SAAnC,GAA+C,aAJ3D;AAKLkC,gBAAAA,YAAY,EAAEY,KAAK,KAAKpC,cAAc,CAACgC,MAAf,GAAwB,CAAlC,GAAsC,eAAtC,GAAwD;AALjE,eAHT;AAUE,cAAA,YAAY,EAAGc,CAAD,IAAO;AACnB,oBAAIrE,eAAe,KAAKgG,MAAM,CAACnF,KAA/B,EAAsC;AACpCwD,kBAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAekB,eAAf,GAAiC,SAAjC;AACD;AACF,eAdH;AAeE,cAAA,YAAY,EAAGnB,CAAD,IAAO;AACnB,oBAAIrE,eAAe,KAAKgG,MAAM,CAACnF,KAA/B,EAAsC;AACpCwD,kBAAAA,CAAC,CAACnC,MAAF,CAASoC,KAAT,CAAekB,eAAf,GAAiC,aAAjC;AACD;AACF,eAnBH;AAoBE,cAAA,OAAO,EAAE,MAAM;AACbvF,gBAAAA,kBAAkB,CAAC+F,MAAM,CAACnF,KAAR,CAAlB;AACAV,gBAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACD,eAvBH;AAAA,qCAyBE;AAAK,gBAAA,SAAS,EAAC,mDAAf;AAAA,wCACE;AAAK,kBAAA,SAAS,EAAC,eAAf;AAAA,0CACE;AAAK,oBAAA,SAAS,EAAC,aAAf;AAAA,8BAA8B6F,MAAM,CAACxE;AAArC;AAAA;AAAA;AAAA;AAAA,0BADF,EAEGwE,MAAM,CAAClE,QAAP,iBACC;AAAK,oBAAA,SAAS,EAAC,YAAf;AAA4B,oBAAA,KAAK,EAAE;AAAEc,sBAAAA,QAAQ,EAAE;AAAZ,qBAAnC;AAAA,2CACWoD,MAAM,CAAClE,QAAP,CAAgB2C,SAAhB,CAA0B,CAA1B,EAA6B,CAA7B,CADX;AAAA;AAAA;AAAA;AAAA;AAAA,0BAHJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBADF,eASE;AAAK,kBAAA,SAAS,EAAC,2BAAf;AAAA,0CACE;AAAM,oBAAA,SAAS,EAAC,uBAAhB;AAAwC,oBAAA,KAAK,EAAE;AAAE7B,sBAAAA,QAAQ,EAAE;AAAZ,qBAA/C;AAAA,8BACGoD,MAAM,CAACvE;AADV;AAAA;AAAA;AAAA;AAAA,0BADF,EAIGzB,eAAe,KAAKgG,MAAM,CAACnF,KAA3B,iBACC;AAAM,oBAAA,KAAK,EAAE;AAAEuC,sBAAAA,KAAK,EAAE,SAAT;AAAoBR,sBAAAA,QAAQ,EAAE;AAA9B,qBAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BALJ;AAAA;AAAA;AAAA;AAAA;AAAA,wBATF;AAAA;AAAA;AAAA;AAAA;AAAA;AAzBF,eACOoD,MAAM,CAACnF,KADd;AAAA;AAAA;AAAA;AAAA,oBADD,CAtBH;AAAA;AAAA;AAAA;AAAA;AAAA,kBA3BJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBAXF;AAAA;AAAA;AAAA;AAAA;AAAA,cAVF,eAwHE,QAAC,gBAAD;AAAA;AAAA;AAAA;AAAA,cAxHF;AAAA;AAAA;AAAA;AAAA;AAAA,YARF,eAoIE;AAAK,MAAA,SAAS,EAAC,SAAf;AAAA,8BACE;AAAK,QAAA,SAAS,EAAC,iBAAf;AAAA,+BACE,QAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cADF,eAIE;AAAK,QAAA,SAAS,EAAC,iBAAf;AAAA,+BACE,QAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cAJF;AAAA;AAAA;AAAA;AAAA;AAAA,YApIF,EA8IG,CAAChB,OAAO,IAAIF,iBAAiB,CAACE,OAA9B,kBACC;AACE,MAAA,SAAS,EAAC,2FADZ;AAEE,MAAA,KAAK,EAAE;AACLgG,QAAAA,MAAM,EAAE,IADH;AAEL/C,QAAAA,UAAU,EAAE,oBAFP;AAGLI,QAAAA,cAAc,EAAE;AAHX,OAFT;AAAA,6BAQE;AACE,QAAA,SAAS,EAAC,iBADZ;AAEE,QAAA,KAAK,EAAE;AACLJ,UAAAA,UAAU,EAAE,2BADP;AAELC,UAAAA,YAAY,EAAE,MAFT;AAGLG,UAAAA,cAAc,EAAE,YAHX;AAILF,UAAAA,MAAM,EAAE,oCAJH;AAKLC,UAAAA,SAAS,EAAE;AALN,SAFT;AAAA,gCAUE;AACE,UAAA,SAAS,EAAC,qBADZ;AAEE,UAAA,KAAK,EAAE;AACLR,YAAAA,KAAK,EAAE,MADF;AAELC,YAAAA,MAAM,EAAE,MAFH;AAGLU,YAAAA,KAAK,EAAE;AAHF,WAFT;AAOE,UAAA,IAAI,EAAC,QAPP;AAAA,iCASE;AAAM,YAAA,SAAS,EAAC,iBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA,gBAVF,eAqBE;AAAG,UAAA,SAAS,EAAC,gBAAb;AAA8B,UAAA,KAAK,EAAE;AAAEA,YAAAA,KAAK,EAAE;AAAT,WAArC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBArBF;AAAA;AAAA;AAAA;AAAA;AAAA;AARF;AAAA;AAAA;AAAA;AAAA,YA/IJ,eAoLE;AAAO,MAAA,GAAG,MAAV;AAAA,gBAAa;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlCM;AAAA;AAAA;AAAA;AAAA,YApLF;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;AA0ND,CA3oBD;;GAAM1D,sB;;KAAAA,sB;AA6oBN,eAAeA,sBAAf", "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ChatState } from '../../context/AllProviders';\r\n\r\nconst PageAnalyticsDashboard = () => {\r\n  const {\r\n    pageAnalyticsData,\r\n    onlineUsersData,\r\n    loading\r\n  } = ChatState();\r\n\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [selectedWebsite, setSelectedWebsite] = useState('all');\r\n  const [showWebsiteDropdown, setShowWebsiteDropdown] = useState(false);\r\n\r\n  // Utility functions\r\n  const formatNumber = (num) => {\r\n    const numValue = Number(num);\r\n    if (isNaN(numValue) || !isFinite(numValue)) return '0';\r\n    \r\n    if (numValue >= 1000000) return (numValue / 1000000).toFixed(1) + 'M';\r\n    if (numValue >= 1000) return (numValue / 1000).toFixed(1) + 'K';\r\n    return numValue.toString();\r\n  };\r\n\r\n  const safeGetValue = (value, fallback = 0) => {\r\n    const numValue = Number(value);\r\n    return isNaN(numValue) || !isFinite(numValue) ? fallback : numValue;\r\n  };\r\n\r\n  const getEventColor = (eventType) => {\r\n    const colors = {\r\n      'click': '#3B82F6',\r\n      'page_view': '#10B981', \r\n      'widget_open': '#8B5CF6',\r\n      'widget_close': '#8B5CF6',\r\n      'whatsapp_send_click': '#25D366',\r\n      'auto_widget_open': '#F59E0B',\r\n      'button_click': '#EF4444',\r\n      'link_click': '#06B6D4',\r\n      'scroll': '#84CC16',\r\n      'page_enter': '#10B981',\r\n      'page_exit': '#F97316'\r\n    };\r\n    return colors[eventType] || '#6B7280';\r\n  };\r\n\r\n  const formatEventName = (eventType) => {\r\n    const names = {\r\n      'whatsapp_send_click': 'WhatsApp Click',\r\n      'auto_widget_open': 'Auto Widget Open',\r\n      'widget_open': 'Widget Open',\r\n      'widget_close': 'Widget Close',\r\n      'button_click': 'Button Click',\r\n      'link_click': 'Link Click',\r\n      'page_view': 'Page View',\r\n      'page_enter': 'Page Enter',\r\n      'page_exit': 'Page Exit'\r\n    };\r\n    return names[eventType] || eventType.replace('_', ' ');\r\n  };\r\n\r\n  // ✅ CRITICAL FIX: Website filtering logic\r\n  const websiteBreakdown = onlineUsersData.websiteWidgetBreakdown || [];\r\n  const websiteOptions = [\r\n    { value: 'all', label: 'All Websites', count: onlineUsersData.totalOnline || 0 },\r\n    ...websiteBreakdown.map(item => ({\r\n      value: `${item.websiteUrl}|${item.widgetId}`,\r\n      label: item.websiteUrl || 'Unknown Website',\r\n      widgetId: item.widgetId,\r\n      count: item.userCount\r\n    }))\r\n  ];\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (showWebsiteDropdown && !event.target.closest('.website-dropdown-container')) {\r\n        setShowWebsiteDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [showWebsiteDropdown]);\r\n\r\n  const ConnectionStatus = () => (\r\n    <div className=\"d-flex align-items-center gap-2\">\r\n      <div className=\"position-relative\">\r\n        <div \r\n          className={`rounded-circle ${onlineUsersData.isConnected ? 'bg-success' : 'bg-danger'}`} \r\n          style={{ width: '10px', height: '10px' }}\r\n        ></div>\r\n        {onlineUsersData.isConnected && (\r\n          <div \r\n            className=\"position-absolute top-0 start-0 rounded-circle bg-success animate-ping\" \r\n            style={{ width: '10px', height: '10px', opacity: '0.75' }}\r\n          ></div>\r\n        )}\r\n      </div>\r\n      <span className={`fw-medium ${onlineUsersData.isConnected ? 'text-success' : 'text-danger'}`} style={{ fontSize: '14px' }}>\r\n        {onlineUsersData.isConnected ? 'Live' : 'Offline'}\r\n      </span>\r\n    </div>\r\n  );\r\n\r\n  const TopPages = () => (\r\n    <div \r\n      className=\"position-relative overflow-hidden\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\r\n        borderRadius: '20px',\r\n        border: '1px solid rgba(226, 232, 240, 0.8)',\r\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n        backdropFilter: 'blur(10px)',\r\n        height: '480px'\r\n      }}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"p-4 border-bottom\" style={{ borderColor: 'rgba(226, 232, 240, 0.5)' }}>\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h6 className=\"mb-1 fw-bold\" style={{ color: '#1e293b', fontSize: '18px' }}>\r\n              Active Pages\r\n            </h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '13px' }}>\r\n              Real-time user activity\r\n            </p>\r\n          </div>\r\n          <div \r\n            className=\"badge d-flex align-items-center gap-1 px-3 py-2\"\r\n            style={{\r\n              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              color: 'white',\r\n              fontSize: '12px',\r\n              fontWeight: '600'\r\n            }}\r\n          >\r\n            <span>{(onlineUsersData.topPages || []).length}</span>\r\n            <span>pages</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"p-4\" style={{ height: 'calc(100% - 80px)', overflowY: 'auto' }}>\r\n        {(onlineUsersData.topPages || []).length > 0 ? (\r\n          <div className=\"d-flex flex-column gap-3\">\r\n            {(onlineUsersData.topPages || []).slice(0, 6).map((page, index) => {\r\n              const userCount = safeGetValue(page.users, 0);\r\n              const maxUsers = Math.max(...(onlineUsersData.topPages || []).map(p => safeGetValue(p.users, 0)), 1);\r\n              const widthPercentage = Math.min(100, (userCount / maxUsers) * 100);\r\n              \r\n              return (\r\n                <div \r\n                  key={index} \r\n                  className=\"position-relative p-3 transition-all\"\r\n                  style={{\r\n                    background: 'rgba(255, 255, 255, 0.7)',\r\n                    borderRadius: '16px',\r\n                    border: '1px solid rgba(226, 232, 240, 0.6)',\r\n                    backdropFilter: 'blur(8px)',\r\n                    transition: 'all 0.3s ease',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.transform = 'translateY(-2px)';\r\n                    e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.transform = 'translateY(0px)';\r\n                    e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                >\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div className=\"d-flex align-items-center gap-3\">\r\n                      <div \r\n                        className=\"d-flex align-items-center justify-content-center\"\r\n                        style={{\r\n                          width: '32px',\r\n                          height: '32px',\r\n                          borderRadius: '10px',\r\n                          background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\r\n                          color: 'white',\r\n                          fontSize: '14px',\r\n                          fontWeight: '600'\r\n                        }}\r\n                      >\r\n                        {index + 1}\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"mb-0 fw-semibold\" style={{ fontSize: '14px', color: '#1e293b' }}>\r\n                          {page.path === '/' ? 'Homepage' : (page.path?.length > 25 ? page.path.substring(0, 25) + '...' : page.path) || 'Unknown'}\r\n                        </p>\r\n                        <p className=\"mb-0 text-muted\" style={{ fontSize: '12px' }}>\r\n                          {userCount} active {userCount === 1 ? 'user' : 'users'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div \r\n                      className=\"badge d-flex align-items-center justify-content-center\"\r\n                      style={{\r\n                        background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,\r\n                        color: 'white',\r\n                        borderRadius: '12px',\r\n                        fontSize: '12px',\r\n                        fontWeight: '600',\r\n                        minWidth: '40px',\r\n                        height: '28px'\r\n                      }}\r\n                    >\r\n                      {userCount}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Modern Progress Bar */}\r\n                  <div \r\n                    className=\"position-relative\"\r\n                    style={{\r\n                      height: '6px',\r\n                      background: 'rgba(226, 232, 240, 0.5)',\r\n                      borderRadius: '6px',\r\n                      overflow: 'hidden'\r\n                    }}\r\n                  >\r\n                    <div \r\n                      className=\"position-absolute top-0 start-0 h-100\"\r\n                      style={{\r\n                        width: `${widthPercentage}%`,\r\n                        background: `linear-gradient(90deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}cc)`,\r\n                        borderRadius: '6px',\r\n                        transition: 'width 0.5s ease'\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        ) : (\r\n          <div className=\"d-flex flex-column align-items-center justify-content-center h-100 text-center\">\r\n            <div \r\n              className=\"mb-3 d-flex align-items-center justify-content-center\"\r\n              style={{\r\n                width: '80px',\r\n                height: '80px',\r\n                borderRadius: '20px',\r\n                background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\r\n                color: '#64748b'\r\n              }}\r\n            >\r\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1.5\">\r\n                <path d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\r\n              </svg>\r\n            </div>\r\n            <h6 className=\"mb-2 fw-semibold\" style={{ color: '#475569' }}>No Active Pages</h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '14px' }}>\r\n              Page activity will appear here when users visit\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const TopInteractions = () => (\r\n    <div \r\n      className=\"position-relative overflow-hidden\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\r\n        borderRadius: '20px',\r\n        border: '1px solid rgba(226, 232, 240, 0.8)',\r\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n        backdropFilter: 'blur(10px)',\r\n        height: '480px'\r\n      }}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"p-4 border-bottom\" style={{ borderColor: 'rgba(226, 232, 240, 0.5)' }}>\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h6 className=\"mb-1 fw-bold\" style={{ color: '#1e293b', fontSize: '18px' }}>\r\n              User Interactions\r\n            </h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '13px' }}>\r\n              Today's activity breakdown\r\n            </p>\r\n          </div>\r\n          <div \r\n            className=\"badge d-flex align-items-center gap-1 px-3 py-2\"\r\n            style={{\r\n              background: 'linear-gradient(135deg, #10b981, #059669)',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              color: 'white',\r\n              fontSize: '12px',\r\n              fontWeight: '600'\r\n            }}\r\n          >\r\n            Today\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"p-4\" style={{ height: 'calc(100% - 80px)', overflowY: 'auto' }}>\r\n        {(pageAnalyticsData.behavior?.interactions || []).length > 0 ? (\r\n          <div className=\"d-flex flex-column gap-3\">\r\n            {(pageAnalyticsData.behavior?.interactions || []).slice(0, 7).map((interaction, index) => {\r\n              let count = safeGetValue(interaction.count, 0);\r\n              // Divide page_exit count by 2 to fix double counting issue\r\n              if (interaction.event_type === 'page_exit') {\r\n                count = Math.round(count / 2);\r\n              }\r\n              const uniqueUsers = safeGetValue(interaction.unique_users, 0);\r\n              const maxCount = Math.max(...(pageAnalyticsData.behavior?.interactions || []).map(i => safeGetValue(i.count, 0)), 1);\r\n              const widthPercentage = Math.min(100, (count / maxCount) * 100);\r\n              const eventColor = getEventColor(interaction.event_type);\r\n              \r\n              return (\r\n                <div \r\n                  key={index} \r\n                  className=\"position-relative p-3 transition-all\"\r\n                  style={{\r\n                    background: 'rgba(255, 255, 255, 0.7)',\r\n                    borderRadius: '16px',\r\n                    border: '1px solid rgba(226, 232, 240, 0.6)',\r\n                    backdropFilter: 'blur(8px)',\r\n                    transition: 'all 0.3s ease',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.transform = 'translateY(-2px)';\r\n                    e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.transform = 'translateY(0px)';\r\n                    e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                >\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div className=\"d-flex align-items-center gap-3\">\r\n                      <div \r\n                        className=\"d-flex align-items-center justify-content-center\"\r\n                        style={{\r\n                          width: '36px',\r\n                          height: '36px',\r\n                          borderRadius: '12px',\r\n                          background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\r\n                          color: 'white'\r\n                        }}\r\n                      >\r\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                          <circle cx=\"12\" cy=\"12\" r=\"3\"/>\r\n                          <path d=\"m12 1 0 6m0 6 0 6\"/>\r\n                          <path d=\"m17 12-6 0m-6 0 6 0\"/>\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"mb-0 fw-semibold\" style={{ fontSize: '14px', color: '#1e293b' }}>\r\n                          {formatEventName(interaction.event_type)}\r\n                        </p>\r\n                        <p className=\"mb-0 text-muted\" style={{ fontSize: '12px' }}>\r\n                          {uniqueUsers} unique {uniqueUsers === 1 ? 'user' : 'users'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div \r\n                      className=\"badge d-flex align-items-center justify-content-center\"\r\n                      style={{\r\n                        background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,\r\n                        color: 'white',\r\n                        borderRadius: '12px',\r\n                        fontSize: '12px',\r\n                        fontWeight: '600',\r\n                        minWidth: '45px',\r\n                        height: '28px'\r\n                      }}\r\n                    >\r\n                      {formatNumber(count)}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Modern Progress Bar */}\r\n                  <div \r\n                    className=\"position-relative\"\r\n                    style={{\r\n                      height: '6px',\r\n                      background: 'rgba(226, 232, 240, 0.5)',\r\n                      borderRadius: '6px',\r\n                      overflow: 'hidden'\r\n                    }}\r\n                  >\r\n                    <div \r\n                      className=\"position-absolute top-0 start-0 h-100\"\r\n                      style={{\r\n                        width: `${widthPercentage}%`,\r\n                        background: `linear-gradient(90deg, ${eventColor}, ${eventColor}cc)`,\r\n                        borderRadius: '6px',\r\n                        transition: 'width 0.5s ease'\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        ) : (\r\n          <div className=\"d-flex flex-column align-items-center justify-content-center h-100 text-center\">\r\n            <div \r\n              className=\"mb-3 d-flex align-items-center justify-content-center\"\r\n              style={{\r\n                width: '80px',\r\n                height: '80px',\r\n                borderRadius: '20px',\r\n                background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',\r\n                color: '#64748b'\r\n              }}\r\n            >\r\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1.5\">\r\n                <path d=\"M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122\"/>\r\n              </svg>\r\n            </div>\r\n            <h6 className=\"mb-2 fw-semibold\" style={{ color: '#475569' }}>No Interactions Yet</h6>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '14px' }}>\r\n              User interactions will be displayed here\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div \r\n      className=\"min-vh-100 position-relative\"\r\n      style={{\r\n        background: 'transparent',\r\n        padding: '2rem'\r\n      }}\r\n    >\r\n      {/* Modern Header */}\r\n      <div \r\n        className=\"d-flex justify-content-between align-items-center mb-4 p-4\"\r\n        style={{\r\n          background: 'rgba(255, 255, 255, 0.95)',\r\n          borderRadius: '20px',\r\n          backdropFilter: 'blur(20px)',\r\n          border: '1px solid rgba(255, 255, 255, 0.2)',\r\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\r\n        }}\r\n      >\r\n        <div className=\"d-flex align-items-center gap-3\">\r\n          <div>\r\n            <h4 className=\"mb-1 fw-bold\" style={{ color: 'maroon', fontSize: '24px' }}>\r\n              Widget Analytics\r\n            </h4>\r\n            <p className=\"mb-0 text-muted\" style={{ fontSize: '14px' }}>\r\n              Real-time insights and user behavior\r\n            </p>\r\n          </div>\r\n\r\n          {/* Website Filter Dropdown */}\r\n          <div className=\"position-relative website-dropdown-container\" style={{ minWidth: '200px' }}>\r\n            <button\r\n              className=\"btn btn-outline-secondary btn-sm d-flex justify-content-between align-items-center w-100\"\r\n              style={{\r\n                borderRadius: \"10px\",\r\n                fontSize: \"13px\",\r\n                fontWeight: \"500\",\r\n                padding: \"10px 15px\",\r\n                border: \"1px solid #dee2e6\",\r\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\"\r\n              }}\r\n              onClick={() => setShowWebsiteDropdown(!showWebsiteDropdown)}\r\n              disabled={loading}\r\n            >\r\n              <span className=\"text-truncate\">\r\n                {websiteOptions.find(opt => opt.value === selectedWebsite)?.label || 'All Websites'}\r\n                {selectedWebsite !== 'all' && (\r\n                  <span className=\"text-muted ms-1\">\r\n                    ({websiteOptions.find(opt => opt.value === selectedWebsite)?.count || 0})\r\n                  </span>\r\n                )}\r\n              </span>\r\n              <span style={{ fontSize: \"10px\" }}>▼</span>\r\n            </button>\r\n\r\n            {/* Website Dropdown */}\r\n            {showWebsiteDropdown && !loading && (\r\n              <div className=\"position-absolute w-100\" style={{\r\n                top: \"100%\",\r\n                left: 0,\r\n                zIndex: 1000,\r\n                backgroundColor: \"white\",\r\n                border: \"1px solid #dee2e6\",\r\n                borderRadius: \"10px\",\r\n                boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.15)\",\r\n                maxHeight: \"250px\",\r\n                overflowY: \"auto\",\r\n                marginTop: \"8px\"\r\n              }}>\r\n                <div className=\"px-3 py-2 border-bottom\" style={{\r\n                  backgroundColor: \"#f8f9fa\",\r\n                  fontSize: \"12px\",\r\n                  fontWeight: \"600\",\r\n                  color: \"#6c757d\",\r\n                  borderRadius: \"10px 10px 0 0\"\r\n                }}>\r\n                  Select Website ({websiteOptions.length} available)\r\n                </div>\r\n\r\n                {websiteOptions.map((option, index) => (\r\n                  <div\r\n                    key={option.value}\r\n                    className=\"px-3 py-3\"\r\n                    style={{\r\n                      cursor: \"pointer\",\r\n                      fontSize: \"13px\",\r\n                      borderBottom: index < websiteOptions.length - 1 ? \"1px solid #f1f3f4\" : \"none\",\r\n                      backgroundColor: selectedWebsite === option.value ? \"#e3f2fd\" : \"transparent\",\r\n                      borderRadius: index === websiteOptions.length - 1 ? \"0 0 10px 10px\" : \"0\"\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      if (selectedWebsite !== option.value) {\r\n                        e.target.style.backgroundColor = \"#f8f9fa\";\r\n                      }\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      if (selectedWebsite !== option.value) {\r\n                        e.target.style.backgroundColor = \"transparent\";\r\n                      }\r\n                    }}\r\n                    onClick={() => {\r\n                      setSelectedWebsite(option.value);\r\n                      setShowWebsiteDropdown(false);\r\n                    }}\r\n                  >\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div className=\"text-truncate\">\r\n                        <div className=\"fw-semibold\">{option.label}</div>\r\n                        {option.widgetId && (\r\n                          <div className=\"text-muted\" style={{ fontSize: \"11px\" }}>\r\n                            Widget: {option.widgetId.substring(0, 8)}...\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <span className=\"badge bg-primary me-2\" style={{ fontSize: \"11px\" }}>\r\n                          {option.count}\r\n                        </span>\r\n                        {selectedWebsite === option.value && (\r\n                          <span style={{ color: \"#1976d2\", fontSize: \"12px\" }}>✓</span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <ConnectionStatus />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"row g-4\">\r\n        <div className=\"col-12 col-xl-6\">\r\n          <TopPages />\r\n        </div>\r\n        <div className=\"col-12 col-xl-6\">\r\n          <TopInteractions />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modern Loading Indicator */}\r\n      {(loading || pageAnalyticsData.loading) && (\r\n        <div \r\n          className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\r\n          style={{ \r\n            zIndex: 9999,\r\n            background: 'rgba(0, 0, 0, 0.4)',\r\n            backdropFilter: 'blur(8px)'\r\n          }}\r\n        >\r\n          <div \r\n            className=\"text-center p-4\"\r\n            style={{\r\n              background: 'rgba(255, 255, 255, 0.95)',\r\n              borderRadius: '20px',\r\n              backdropFilter: 'blur(20px)',\r\n              border: '1px solid rgba(255, 255, 255, 0.2)',\r\n              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            <div \r\n              className=\"spinner-border mb-3\"\r\n              style={{ \r\n                width: '3rem', \r\n                height: '3rem',\r\n                color: '#667eea'\r\n              }}\r\n              role=\"status\"\r\n            >\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </div>\r\n            <p className=\"mb-0 fw-medium\" style={{ color: '#1e293b' }}>\r\n              Updating Analytics...\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Custom CSS for animations */}\r\n      <style jsx>{`\r\n        .animate-ping {\r\n          animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\r\n        }\r\n        \r\n        @keyframes ping {\r\n          75%, 100% {\r\n            transform: scale(2);\r\n            opacity: 0;\r\n          }\r\n        }\r\n        \r\n        .transition-all {\r\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n        }\r\n        \r\n        /* Custom scrollbar */\r\n        ::-webkit-scrollbar {\r\n          width: 6px;\r\n        }\r\n        \r\n        ::-webkit-scrollbar-track {\r\n          background: rgba(226, 232, 240, 0.3);\r\n          border-radius: 6px;\r\n        }\r\n        \r\n        ::-webkit-scrollbar-thumb {\r\n          background: rgba(148, 163, 184, 0.5);\r\n          border-radius: 6px;\r\n        }\r\n        \r\n        ::-webkit-scrollbar-thumb:hover {\r\n          background: rgba(148, 163, 184, 0.7);\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageAnalyticsDashboard;\r\n"]}, "metadata": {}, "sourceType": "module"}