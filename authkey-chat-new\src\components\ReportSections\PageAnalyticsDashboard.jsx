import React, { useState } from 'react';
import { ChatState } from '../../context/AllProviders';

const PageAnalyticsDashboard = () => {
  const {
    pageAnalyticsData,
    onlineUsersData,
    loading
  } = ChatState();

  const [refreshing, setRefreshing] = useState(false);

  // Utility functions
  const formatNumber = (num) => {
    const numValue = Number(num);
    if (isNaN(numValue) || !isFinite(numValue)) return '0';
    
    if (numValue >= 1000000) return (numValue / 1000000).toFixed(1) + 'M';
    if (numValue >= 1000) return (numValue / 1000).toFixed(1) + 'K';
    return numValue.toString();
  };

  const safeGetValue = (value, fallback = 0) => {
    const numValue = Number(value);
    return isNaN(numValue) || !isFinite(numValue) ? fallback : numValue;
  };

  const getEventColor = (eventType) => {
    const colors = {
      'click': '#3B82F6',
      'page_view': '#10B981', 
      'widget_open': '#8B5CF6',
      'widget_close': '#8B5CF6',
      'whatsapp_send_click': '#25D366',
      'auto_widget_open': '#F59E0B',
      'button_click': '#EF4444',
      'link_click': '#06B6D4',
      'scroll': '#84CC16',
      'page_enter': '#10B981',
      'page_exit': '#F97316'
    };
    return colors[eventType] || '#6B7280';
  };

  const formatEventName = (eventType) => {
    const names = {
      'whatsapp_send_click': 'WhatsApp Click',
      'auto_widget_open': 'Auto Widget Open',
      'widget_open': 'Widget Open', 
      'widget_close': 'Widget Close',
      'button_click': 'Button Click',
      'link_click': 'Link Click',
      'page_view': 'Page View',
      'page_enter': 'Page Enter',
      'page_exit': 'Page Exit'
    };
    return names[eventType] || eventType.replace('_', ' ');
  };

  const ConnectionStatus = () => (
    <div className="d-flex align-items-center gap-2">
      <div className="position-relative">
        <div 
          className={`rounded-circle ${onlineUsersData.isConnected ? 'bg-success' : 'bg-danger'}`} 
          style={{ width: '10px', height: '10px' }}
        ></div>
        {onlineUsersData.isConnected && (
          <div 
            className="position-absolute top-0 start-0 rounded-circle bg-success animate-ping" 
            style={{ width: '10px', height: '10px', opacity: '0.75' }}
          ></div>
        )}
      </div>
      <span className={`fw-medium ${onlineUsersData.isConnected ? 'text-success' : 'text-danger'}`} style={{ fontSize: '14px' }}>
        {onlineUsersData.isConnected ? 'Live' : 'Offline'}
      </span>
    </div>
  );

  const TopPages = () => (
    <div 
      className="position-relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        borderRadius: '20px',
        border: '1px solid rgba(226, 232, 240, 0.8)',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        backdropFilter: 'blur(10px)',
        height: '480px'
      }}
    >
      {/* Header */}
      <div className="p-4 border-bottom" style={{ borderColor: 'rgba(226, 232, 240, 0.5)' }}>
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h6 className="mb-1 fw-bold" style={{ color: '#1e293b', fontSize: '18px' }}>
              Active Pages
            </h6>
            <p className="mb-0 text-muted" style={{ fontSize: '13px' }}>
              Real-time user activity
            </p>
          </div>
          <div 
            className="badge d-flex align-items-center gap-1 px-3 py-2"
            style={{
              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '12px',
              fontWeight: '600'
            }}
          >
            <span>{(onlineUsersData.topPages || []).length}</span>
            <span>pages</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4" style={{ height: 'calc(100% - 80px)', overflowY: 'auto' }}>
        {(onlineUsersData.topPages || []).length > 0 ? (
          <div className="d-flex flex-column gap-3">
            {(onlineUsersData.topPages || []).slice(0, 6).map((page, index) => {
              const userCount = safeGetValue(page.users, 0);
              const maxUsers = Math.max(...(onlineUsersData.topPages || []).map(p => safeGetValue(p.users, 0)), 1);
              const widthPercentage = Math.min(100, (userCount / maxUsers) * 100);
              
              return (
                <div 
                  key={index} 
                  className="position-relative p-3 transition-all"
                  style={{
                    background: 'rgba(255, 255, 255, 0.7)',
                    borderRadius: '16px',
                    border: '1px solid rgba(226, 232, 240, 0.6)',
                    backdropFilter: 'blur(8px)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0px)';
                    e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';
                  }}
                >
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <div className="d-flex align-items-center gap-3">
                      <div 
                        className="d-flex align-items-center justify-content-center"
                        style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '10px',
                          background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,
                          color: 'white',
                          fontSize: '14px',
                          fontWeight: '600'
                        }}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <p className="mb-0 fw-semibold" style={{ fontSize: '14px', color: '#1e293b' }}>
                          {page.path === '/' ? 'Homepage' : (page.path?.length > 25 ? page.path.substring(0, 25) + '...' : page.path) || 'Unknown'}
                        </p>
                        <p className="mb-0 text-muted" style={{ fontSize: '12px' }}>
                          {userCount} active {userCount === 1 ? 'user' : 'users'}
                        </p>
                      </div>
                    </div>
                    <div 
                      className="badge d-flex align-items-center justify-content-center"
                      style={{
                        background: `linear-gradient(135deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}dd)`,
                        color: 'white',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: '600',
                        minWidth: '40px',
                        height: '28px'
                      }}
                    >
                      {userCount}
                    </div>
                  </div>
                  
                  {/* Modern Progress Bar */}
                  <div 
                    className="position-relative"
                    style={{
                      height: '6px',
                      background: 'rgba(226, 232, 240, 0.5)',
                      borderRadius: '6px',
                      overflow: 'hidden'
                    }}
                  >
                    <div 
                      className="position-absolute top-0 start-0 h-100"
                      style={{
                        width: `${widthPercentage}%`,
                        background: `linear-gradient(90deg, ${getEventColor('page_view')}, ${getEventColor('page_view')}cc)`,
                        borderRadius: '6px',
                        transition: 'width 0.5s ease'
                      }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="d-flex flex-column align-items-center justify-content-center h-100 text-center">
            <div 
              className="mb-3 d-flex align-items-center justify-content-center"
              style={{
                width: '80px',
                height: '80px',
                borderRadius: '20px',
                background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',
                color: '#64748b'
              }}
            >
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <h6 className="mb-2 fw-semibold" style={{ color: '#475569' }}>No Active Pages</h6>
            <p className="mb-0 text-muted" style={{ fontSize: '14px' }}>
              Page activity will appear here when users visit
            </p>
          </div>
        )}
      </div>
    </div>
  );

  const TopInteractions = () => (
    <div 
      className="position-relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        borderRadius: '20px',
        border: '1px solid rgba(226, 232, 240, 0.8)',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        backdropFilter: 'blur(10px)',
        height: '480px'
      }}
    >
      {/* Header */}
      <div className="p-4 border-bottom" style={{ borderColor: 'rgba(226, 232, 240, 0.5)' }}>
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h6 className="mb-1 fw-bold" style={{ color: '#1e293b', fontSize: '18px' }}>
              User Interactions
            </h6>
            <p className="mb-0 text-muted" style={{ fontSize: '13px' }}>
              Today's activity breakdown
            </p>
          </div>
          <div 
            className="badge d-flex align-items-center gap-1 px-3 py-2"
            style={{
              background: 'linear-gradient(135deg, #10b981, #059669)',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '12px',
              fontWeight: '600'
            }}
          >
            Today
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4" style={{ height: 'calc(100% - 80px)', overflowY: 'auto' }}>
        {(pageAnalyticsData.behavior?.interactions || []).length > 0 ? (
          <div className="d-flex flex-column gap-3">
            {(pageAnalyticsData.behavior?.interactions || []).slice(0, 7).map((interaction, index) => {
              let count = safeGetValue(interaction.count, 0);
              // Divide page_exit count by 2 to fix double counting issue
              if (interaction.event_type === 'page_exit') {
                count = Math.round(count / 2);
              }
              const uniqueUsers = safeGetValue(interaction.unique_users, 0);
              const maxCount = Math.max(...(pageAnalyticsData.behavior?.interactions || []).map(i => safeGetValue(i.count, 0)), 1);
              const widthPercentage = Math.min(100, (count / maxCount) * 100);
              const eventColor = getEventColor(interaction.event_type);
              
              return (
                <div 
                  key={index} 
                  className="position-relative p-3 transition-all"
                  style={{
                    background: 'rgba(255, 255, 255, 0.7)',
                    borderRadius: '16px',
                    border: '1px solid rgba(226, 232, 240, 0.6)',
                    backdropFilter: 'blur(8px)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 8px 25px -5px rgba(0, 0, 0, 0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0px)';
                    e.target.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';
                  }}
                >
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <div className="d-flex align-items-center gap-3">
                      <div 
                        className="d-flex align-items-center justify-content-center"
                        style={{
                          width: '36px',
                          height: '36px',
                          borderRadius: '12px',
                          background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,
                          color: 'white'
                        }}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <circle cx="12" cy="12" r="3"/>
                          <path d="m12 1 0 6m0 6 0 6"/>
                          <path d="m17 12-6 0m-6 0 6 0"/>
                        </svg>
                      </div>
                      <div>
                        <p className="mb-0 fw-semibold" style={{ fontSize: '14px', color: '#1e293b' }}>
                          {formatEventName(interaction.event_type)}
                        </p>
                        <p className="mb-0 text-muted" style={{ fontSize: '12px' }}>
                          {uniqueUsers} unique {uniqueUsers === 1 ? 'user' : 'users'}
                        </p>
                      </div>
                    </div>
                    <div 
                      className="badge d-flex align-items-center justify-content-center"
                      style={{
                        background: `linear-gradient(135deg, ${eventColor}, ${eventColor}dd)`,
                        color: 'white',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: '600',
                        minWidth: '45px',
                        height: '28px'
                      }}
                    >
                      {formatNumber(count)}
                    </div>
                  </div>
                  
                  {/* Modern Progress Bar */}
                  <div 
                    className="position-relative"
                    style={{
                      height: '6px',
                      background: 'rgba(226, 232, 240, 0.5)',
                      borderRadius: '6px',
                      overflow: 'hidden'
                    }}
                  >
                    <div 
                      className="position-absolute top-0 start-0 h-100"
                      style={{
                        width: `${widthPercentage}%`,
                        background: `linear-gradient(90deg, ${eventColor}, ${eventColor}cc)`,
                        borderRadius: '6px',
                        transition: 'width 0.5s ease'
                      }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="d-flex flex-column align-items-center justify-content-center h-100 text-center">
            <div 
              className="mb-3 d-flex align-items-center justify-content-center"
              style={{
                width: '80px',
                height: '80px',
                borderRadius: '20px',
                background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',
                color: '#64748b'
              }}
            >
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                <path d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
              </svg>
            </div>
            <h6 className="mb-2 fw-semibold" style={{ color: '#475569' }}>No Interactions Yet</h6>
            <p className="mb-0 text-muted" style={{ fontSize: '14px' }}>
              User interactions will be displayed here
            </p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div 
      className="min-vh-100 position-relative"
      style={{
        background: 'transparent',
        padding: '2rem'
      }}
    >
      {/* Modern Header */}
      <div 
        className="d-flex justify-content-between align-items-center mb-4 p-4"
        style={{
          background: 'rgba(255, 255, 255, 0.95)',
          borderRadius: '20px',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
        }}
      >
        <div>
          <h4 className="mb-1 fw-bold" style={{ color: 'maroon', fontSize: '24px' }}>
            Widget Analytics
          </h4>
          <p className="mb-0 text-muted" style={{ fontSize: '14px' }}>
            Real-time insights and user behavior
          </p>
        </div>
        <ConnectionStatus />
      </div>

      {/* Main Content */}
      <div className="row g-4">
        <div className="col-12 col-xl-6">
          <TopPages />
        </div>
        <div className="col-12 col-xl-6">
          <TopInteractions />
        </div>
      </div>

      {/* Modern Loading Indicator */}
      {(loading || pageAnalyticsData.loading) && (
        <div 
          className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
          style={{ 
            zIndex: 9999,
            background: 'rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(8px)'
          }}
        >
          <div 
            className="text-center p-4"
            style={{
              background: 'rgba(255, 255, 255, 0.95)',
              borderRadius: '20px',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
            }}
          >
            <div 
              className="spinner-border mb-3"
              style={{ 
                width: '3rem', 
                height: '3rem',
                color: '#667eea'
              }}
              role="status"
            >
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mb-0 fw-medium" style={{ color: '#1e293b' }}>
              Updating Analytics...
            </p>
          </div>
        </div>
      )}

      {/* Custom CSS for animations */}
      <style jsx>{`
        .animate-ping {
          animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
        
        @keyframes ping {
          75%, 100% {
            transform: scale(2);
            opacity: 0;
          }
        }
        
        .transition-all {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 6px;
        }
        
        ::-webkit-scrollbar-track {
          background: rgba(226, 232, 240, 0.3);
          border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb {
          background: rgba(148, 163, 184, 0.5);
          border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: rgba(148, 163, 184, 0.7);
        }
      `}</style>
    </div>
  );
};

export default PageAnalyticsDashboard;
